package com.chinamobile.iot.sc.common;


import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import static com.chinamobile.iot.sc.exceptions.ExcepStatus.createInstance;
import static com.chinamobile.iot.sc.exceptions.ExcepStatus.getSuccInstance;


/**
 * 统一返回类型
 * @param <T>
 */
@Data
public class BaseAnswer<T> {
    //不传递
	@JsonIgnore
	private ExcepStatus status;
	//状态码
    private String stateCode;
    //成功失败消息
    private String message;
    //返回具体数据
    private T data;

    //返回签名，能力开放接口使用
    private String sign;

    //返回时间戳，能力开放接口使用
    private String timestamp;

    public BaseAnswer() {
        this.status = getSuccInstance();
        this.stateCode = status.getStateCode();
        this.message = status.getMessage();
    }

    public BaseAnswer(String stateCode, String message) {
    	this.stateCode = stateCode;
    	this.message = message;
        this.status = createInstance(stateCode,message);
    }

    public BaseAnswer(BusinessException ex) {
        this(ex.getStatus());
    }

    public BaseAnswer(ExcepStatus statusEnum) {
    	this.status = statusEnum;
        this.stateCode = statusEnum.getStateCode();
        this.message = statusEnum.getMessage();
    }

    public BaseAnswer<T> setData(T data) {
        this.data = data;
        return this;
    }

    public BaseAnswer setStatus(ExcepStatus statusEnum) {
    	this.status = statusEnum;
        this.stateCode = statusEnum.getStateCode();
        this.message = statusEnum.getMessage();
        return this;
    }

    public BaseAnswer setMessage(String msg) {
        this.message = msg;
        this.status = createInstance(stateCode,msg);
        return this;
    }

    public static BaseAnswer success(Object data){
        BaseAnswer baseAnswer = new BaseAnswer();
        baseAnswer.setData(data);
        return baseAnswer;
    }

    public static BaseAnswer success(String timestamp,String sign,String data){
        BaseAnswer baseAnswer = new BaseAnswer();
        baseAnswer.setData(data);
        baseAnswer.setTimestamp(timestamp);
        baseAnswer.setSign(sign);
        return baseAnswer;
    }

    public static BaseAnswer success(Object data, String apiKey){
        String dataJson = JSON.toJSONString(data);
        String timestamp = String.valueOf(System.currentTimeMillis());
        String sign = BaseUtils.getOpenAnswerSign(dataJson,timestamp,apiKey);
        return success(timestamp,sign,dataJson);
    }

    @Override
    public String toString() {
        return "BaseAnswer [stateCode=" + stateCode + ", message=" + message
                + ", data=" + data + "]";
    }

}
