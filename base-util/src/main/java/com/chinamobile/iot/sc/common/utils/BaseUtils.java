package com.chinamobile.iot.sc.common.utils;


import com.chinamobile.iot.sc.common.Constant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.util.DigestUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.*;

/**
 * 基础工具类
 */
public class BaseUtils {
    private static final String CHAR_SET = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final Random random = new SecureRandom();
    /**
     * uuid 去除- 后的信息
     * @return
     */
    public static String uuid(){
        return UUID.randomUUID().toString().replace("-","");
    }

    /**
     * 日期信息重值
     * @param date 日期
     * @param addNum 添加的数
     * @param unit 单位
     * @return 加减后的日期
     */
    public static Date getResetDate(Date date, int addNum, TimeUnitEnum unit) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (addNum != 0){
            calendar.add(unit.value(), addNum);
        }
        return calendar.getTime();
    }


    public static Date getResetDate(Date date, long addMillis) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (addMillis != 0){
            calendar.setTimeInMillis(date.getTime()+addMillis);
        }
        return calendar.getTime();
    }


    public static String getCaptchaSessionIdKey(String sessionId){
        return Constant.REDIS_PRE_KEY_CAPTCHA_SESSIONID + sessionId;
    }

    /**
     * 获取短信验证码时，存入redis的key
     * @param phone
     * @return
     */
    public static String getSMSValidKey(String phone){
        return Constant.REDIS_PRE_KEY_SMS_VALID + phone;
    }

    /**
     * 获取修改用户短信验证码时，存入redis的key
     * @param phone
     * @return
     */
    public static String getEditSMSKey(String phone){
        return Constant.REDIS_PRE_KEY_EDIT_SMS + phone;
    }

    /**
     * 获取OS验证验证码错误次数记录错误次数，存入redis的key
     * @param phone
     * @return
     */
    public static String getNumErrorsKey(String phone){
        return Constant.REDIS_PRE_KEY_SMS_NUM_ERRORS_KEY + phone;
    }

    /**
     * 获取Retail验证验证码错误次数记录错误次数，存入redis的key
     * @param phone
     * @return
     */
    public static String getNumErrorsRetailKey(String phone){
        return Constant.REDIS_PRE_KEY_SMS_NUM_ERRORS_RETAIL_KEY + phone;
    }

    /**
     * 用于获取短信验证码验证通过后，进行下一步操作使用的保存在redis的key
     * @param phone
     * @param type
     * @return
     */
    public static String getSmsCodeValidNextKey(String phone,String type){
        return Constant.REDIS_PRE_KEY_SMS_VALID_NEXT_KEY + phone + ":" + type;
    }


    /**
     * 获取导出文件短信验证码时，存入redis的key
     * @param phone
     * @return
     */
    public static String getExportSMSValidKey(String phone){
        return Constant.REDIS_PRE_KEY_EXPORT_SMS_VALID + phone;
    }

    /**
     * 对象转hashMap,key-value 都是String类型。 null的value会被忽略
     */
    public static Map<String, String> objectToStringMap(Object object) {
        Map<String, String> dataMap = new HashMap<>();
        Class<?> clazz = object.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            try {
                field.setAccessible(true);
                Object  o = field.get(object);
                if(o != null){
                    String s = String.valueOf(o);
                    dataMap.put(field.getName(), s);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return dataMap;
    }

    public static String getSha1(String str) {
        char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'a', 'b', 'c', 'd', 'e', 'f' };
        try {
            MessageDigest mdTemp = MessageDigest.getInstance("SHA1");
            mdTemp.update(str.getBytes("UTF-8"));
            byte[] md = mdTemp.digest();
            int j = md.length;
            char buf[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                buf[k++] = hexDigits[byte0 >>> 4 & 0xf];
                buf[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(buf);
        } catch (Exception e) {
            return null;
        }
    }
    /**
     * 拷贝属性，忽略为null的属性
     *
     * @param src    源对象
     * @param target 目标对象
     */
    public static void copyNonNullProperties(Object src, Object target) {
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }
    /**
     * 获取为null的属性名数组
     *
     * @param source 源对象
     * @return 包含null值的属性名数组
     */
    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }

        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }
    /**
     * 判断对象是否至少有一个属性有值（空字符串也是无值）
     */
    public static boolean anyFiledHasValue(Object param) {
        Class<?> clazz = param.getClass();
        Field[] declaredFields = clazz.getDeclaredFields();
        return Arrays.stream(declaredFields).anyMatch(f -> {
            f.setAccessible(true);
            try {
                Object o = f.get(param);
                if (o != null) {
                    if(o instanceof String){
                        String str = (String) o;
                        if(StringUtils.isNotBlank(str)){
                            return true;
                        }
                    }else {
                        return true;
                    }
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            return false;
        });
    }

    //开放能力header签名
    public static String getOpenSign(String appId,String rand,
                          String timestamp,String apiKey) {
        StringBuilder sb = new StringBuilder();
        sb.append(appId);
        sb.append(rand);
        sb.append(timestamp);
        sb.append(apiKey);

        String sign = sb.toString();
        return DigestUtils.md5DigestAsHex(
                sign.getBytes(StandardCharsets.UTF_8));
    }

    public static String getOpenAnswerSign(String dataJson, String timestamp,String apiKey) {
        StringBuilder sb = new StringBuilder();
        sb.append(dataJson);
        sb.append(timestamp);
        sb.append(apiKey);

        String sign = sb.toString();
        return DigestUtils.md5DigestAsHex(
                sign.getBytes(StandardCharsets.UTF_8));
    }

    public static String aesEncrypt(String input, String key) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(), "AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        byte[] encrypted = cipher.doFinal(input.getBytes());
        return Base64.getEncoder().encodeToString(encrypted);
    }

    public static String aesDecrypt(String input, String key) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(), "AES");
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(input));
        return new String(decryptedBytes);
    }

    /**
     * 生成一个指定长度的随机字符串。
     *
     * @param length 生成字符串的长度。
     * @return 生成的随机字符串。
     */
    public static String generateRandomString(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("Length must be a positive number");
        }

        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            sb.append(CHAR_SET.charAt(random.nextInt(CHAR_SET.length())));
        }

        return sb.toString();
    }

    public static void main(String[] args) {
        String rand = generateRandomString(16);
        String timestamp = String.valueOf(System.currentTimeMillis());
        String appId = "1250135206490095616";
        String apiKey = "RGYBj6pIxQIV6CQzHtjMVEFv9xilGxG7";

        System.out.println("rand:"+rand);
        System.out.println("timestamp:"+timestamp);
        System.out.println("appId:"+appId);
        System.out.println("sign:"+getOpenSign(appId,rand,timestamp,apiKey));
    }

}
