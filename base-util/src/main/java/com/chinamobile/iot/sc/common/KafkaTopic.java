package com.chinamobile.iot.sc.common;

/**
 * kafka topic枚举
 * <AUTHOR>
 */
public enum KafkaTopic {
    /**
     * 订单topic
     */
    SUPPLY_CHAIN_ORDER(0, "supply_chain_order"),

    /**
     * websocket推送
     */
    SUPPLY_CHAIN_WEBSOCKET(1, "supply_chain_websocket"),

    /**
     * 小程序用户订单
     */
    MINI_PROGRAM_USER_ORDER(2, "mini_program_user_order"),

    /**
     * 网格数据表
     */
    SUPPLY_CHAIN_RISE_ORDER_2C_GRID(3, "supply_chain.rise_order_2c_grid");

    /**
     * 枚举编码
     */
    private final Integer code;

    /**
     * 枚举值
     */
    private final String topic;

    KafkaTopic(Integer code, String topic) {
        this.code = code;
        this.topic = topic;
    }

    public Integer getCode() {
        return code;
    }

    public String getTopic() {
        return topic;
    }
}
