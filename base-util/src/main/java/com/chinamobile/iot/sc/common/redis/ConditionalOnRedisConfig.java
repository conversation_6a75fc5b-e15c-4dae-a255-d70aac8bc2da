package com.chinamobile.iot.sc.common.redis;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;

import java.lang.annotation.*;

@Target({ ElementType.TYPE, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@ConditionalOnExpression("!T(org.apache.commons.lang.StringUtils).isEmpty('${spring.redis.cluster.nodes:}')" +
        "|| !T(org.apache.commons.lang.StringUtils).isEmpty('${spring.redis.host:}')")
public @interface ConditionalOnRedisConfig {
}
