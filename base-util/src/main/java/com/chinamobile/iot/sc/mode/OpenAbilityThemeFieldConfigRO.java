package com.chinamobile.iot.sc.mode;

import lombok.Data;

import java.util.Date;

/**
 * 开放能力应用主题字段配置
 *
 * <AUTHOR>
@Data
public class OpenAbilityThemeFieldConfigRO {
    /**
     * 主键
     */
    private String id;

    /**
     * 能力开放部门ID
     */
    private String organizationId;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 规则ID
     */
    private String ruleId;
    /**
     * 字段ID
     *
     */
    private String fieldId;

    /**
     * 字段名称
     *
     */
    private String name;

    /**
     * 主题ID
     *
     */
    private String themeId;

    /**
     * 主题编码
     *
     */
    private String themeCode;

    /**
     * 主题内容Id，标识接口或者订阅内容；主题本身的配置字段为null
     */
    private String contentId;

    /**
     * 字段描述
     *
     */
    private String description;

    /**
     * 字段类型，string-字符串，number-数值型，bool-布尔型，time-时间型，enum-枚举型
     */
    private String type;

    /**
     * 枚举型时的取值列表所转换的json字符串

     */
    private String rangeJson;

    /**
     * 规则类型，equal（等于）、in（多值匹配）、greaterThan（大于）、lessThan（小于）、like（模糊匹配）、greaterEqualThan（大于等于）、lessEqualThan（小于等于）、notEqual（不等于）、notIn（不在范围内）、notLike（不符合模糊匹配）
     */
    private String ruleType;

    /**
     * 所配置筛选规则的筛选的单个值，只要筛选规则不是in、notIn，其配置的筛选值都用这个字段存储
     *
     */
    private String ruleValueObj;

    /**
     * 数组，in、notIn筛选规则所配置的值保存字段
     *
     */
    private String ruleRangeList;

    /**
     * 创建时间
     *
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}