package com.chinamobile.iot.sc.exceptions;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SERVER_INTERNAL_ERROR;


/**
 * 自定义异常
 */
public class BusinessException extends RuntimeException {
    @Getter
    private  Throwable rootException;
    @Getter
    private  ExcepStatus status = SERVER_INTERNAL_ERROR;

    public BusinessException() {
		super();
	}

    public BusinessException(Throwable e ){
        super(e);
        this.rootException = e;
    }

    public BusinessException(String errorCode, String errorMessage){
        this(errorCode,errorMessage,null);
    }
    public BusinessException(String errorCode, String errorMessage, Throwable e ){
        super((errorCode == null ? "" : errorCode+",") + errorMessage,e);
        this.status = ExcepStatus.createInstance(errorCode,errorMessage);
        this.rootException = e;
    }

    public BusinessException(ExcepStatus status) {
        super(status.getStateCode()+"," +status.getMessage());
        this.status = status;
    }

    public BusinessException(ExcepStatus status, String overwriteMsg) {
        super(status.getStateCode()+"," +(StringUtils.isNotBlank(overwriteMsg)?overwriteMsg:status.getMessage()));
        if(StringUtils.isNotBlank(overwriteMsg)) {
            this.status = ExcepStatus.createInstance(status.getStateCode(), overwriteMsg);
        } else {
            this.status = status;
        }
    }
}
