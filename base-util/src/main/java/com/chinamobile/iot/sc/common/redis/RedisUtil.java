package com.chinamobile.iot.sc.common.redis;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.IntFunction;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;

public class RedisUtil extends RedisTemplate<Serializable, Serializable> {
    @Autowired
    private RedissonClient redissonClient ;

    /**
     * 直接获取key反序列化的对象
     * @param key
     * @param <T>
     * @return
     */
    public <T> T get(String key) {
        Serializable val = super.opsForValue().get(key);
        if(isNull(val)){
            return null;
        }
        return ((T)val);
    }

    /**
     * 原子计数操作
     * */
    public Long incr(String key) {
        Long val = super.opsForValue().increment(key);
        if(isNull(val)){
            return null;
        }
        return val;
    }

    /**
     *  获取哈希存储对象
     * @param key
     * @param filed
     * @param <T>
     * @return
     */
    public <T> T hget(String key,Object filed) {
        Object val = super.opsForHash().get(key,filed);
        if(isNull(val)){
            return null;
        }
        return ((T)val);
    }

    public <T> T hgetAll(String key) {
        Map val = super.opsForHash().entries(key);
        if(CollectionUtils.isEmpty(val)){
            return null;
        }
        return ((T)val);
    }

    /**
     * 智能锁，不需要关注锁的释放，只需要在需要加锁的位置加上锁，然后在proceessor中处理自己的逻辑既可
     * 不加过期时间的锁，默认30秒过期，默认情况下实现了 锁续命操作（一个线程 10执行一次锁判断）
     *
     * @param lockKey
     * @param proc    处理器，处理业务逻辑，函数式模板业务方法
     * @param <T>     返回对象类型
     * @return
     * @throws Exception
     */
    public <T> T smartLock(String lockKey, Processor<T> proc) {
        RLock rl = redissonClient.getLock(lockKey);
        try {
            rl.lock();
            return proc.doProcess();
        } finally {
            //必须是当前线程持有的锁才被关闭，否则关闭容易导致锁失效，eg 如下场景
            /**
             * A服务获取到锁 0，此时redis的master关掉并且锁 0未同步到slave节点，对应slave接管master角色成为master;
             * 此时 B服务获取锁，发现锁0空闲，获取到锁0 ，此时A服务执行完，释放锁0（但是此时0的持有者为服务B），问题就出现了，A释放了B创建并持有的锁
             * 高并发情况下，C\D....服务依次不断的成功获取到锁，然后被其他线程又不断释放，导致锁失效
             *
             * 所以不要忽略这个判断，上述情况一旦在高并发情况下出现就是灾难性的，并且很难排查
             */
            if (rl.isHeldByCurrentThread()) {
                rl.unlock();
            }
        }
    }

    /**
     * @param lockKey
     * @param expireMilliSeconds 锁自动过期时间，防止泄露
     * @param proc 处理器，处理业务逻辑，函数式模板业务方法
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> T smartlock(String lockKey, Long expireMilliSeconds,Processor<T> proc) {
        RLock rl = redissonClient.getLock(lockKey);
        try {
            rl.lock(expireMilliSeconds,TimeUnit.MILLISECONDS);
            return proc.doProcess();
        } finally {
            if (rl.isHeldByCurrentThread()) {
                rl.unlock();
            }
        }
    }

    /**
     * 智能锁，不需要关注锁的释放，只需要在需要加锁的位置加上锁，然后在proceessor中处理自己的逻辑既可
     * 不加过期时间的锁，默认30秒过期，默认情况下实现了 锁续命操作（一个线程 10执行一次锁判断）
     *
     * @param lockKeys  锁数组，使用连锁
     * @param proc    处理器，处理业务逻辑，函数式模板业务方法
     * @param <T>     返回对象类型
     * @return
     * @throws Exception
     */
    public <T> T smartLock(List<String> lockKeys, Processor<T> proc) {
        RLock[] lockArray =lockKeys.stream().map(key -> redissonClient.getLock(key)).toArray(RLock[]::new);
        RLock rl = redissonClient.getMultiLock(lockArray);
        try {
            rl.lock();
            return proc.doProcess();
        } finally {
            //联锁不支持当前线程是否已经加锁判定
            rl.unlock();
        }
    }

    /**
     * @param lockKeys
     * @param expireMilliSeconds 锁自动过期时间，防止泄露
     * @param proc 处理器，处理业务逻辑，函数式模板业务方法
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> T smartlock(List<String> lockKeys, Long expireMilliSeconds,Processor<T> proc) {
        RLock[] lockArray =lockKeys.stream().map(key -> redissonClient.getLock(key)).toArray(RLock[]::new);
        RLock rl = redissonClient.getMultiLock(lockArray);
        try {
            rl.lock(expireMilliSeconds,TimeUnit.MILLISECONDS);
            return proc.doProcess();
        } finally {
            //联锁不支持当前线程是否已经加锁判定
            rl.unlock();
        }
    }
}
