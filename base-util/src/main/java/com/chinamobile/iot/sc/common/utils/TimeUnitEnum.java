package com.chinamobile.iot.sc.common.utils;

import java.util.Calendar;

public enum TimeUnitEnum {
    YEAR(Calendar.YEAR),
    MONTH(Calendar.MONTH),
    DAY(Calendar.DAY_OF_YEAR),
    HOUR(Calendar.HOUR_OF_DAY),
    MINUTE(Calendar.MINUTE),
    SECOND(Calendar.SECOND),
    MILLISECOND(Calendar.MILLISECOND),
    ;

    private final int format;
    TimeUnitEnum(int format) {
        this.format = format;
    }
    public int value(){
        return format;
    }
}
