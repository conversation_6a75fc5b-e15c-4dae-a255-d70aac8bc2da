package com.chinamobile.iot.sc.common.utils;

import java.util.regex.Pattern;

/**
 * @package: com.chinamobile.iot.sc.utils
 * @ClassName: RegexUtil
 * @description: 正则校验工具类
 * @author: zyj
 * @create: 2021/11/15 10:06
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public class RegexUtil {

    public static final String OPERATOR_NAME_PATTERN = "^[\\u4e00-\\u9fa5a-zA-Z]+";
    public static final String PARTNER_NAME_PATTERN = "^[\\u4e00-\\u9fa5]{1,20}$";
    public static final String CONTACT_NAME_PATTERN = "^[\\u4e00-\\u9fa5]{2,6}$";
    public static final String COMPANY_PATTERN = "^[\\u4e00-\\u9fa5]{2,15}$";
    public static final String PHONE_PATTERN = "^((13[0-9])|(14([0,1,4-9]))|(15([0-3,5-9]))|(16[2,5,6,7])|(17[0-8])|(18[0-9])|(19([0-3,5-9])))\\d{8}$";
    public static final String EMAIL_PATTERN = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";
    public static final String RETURN_CONTACT_PATTERN = "^[\\u4e00-\\u9fa5]{2,5}$";
    public static final String NUMBER_PATTERN = "^[0-9]*$";
    public static final String PRICE_DECIMAL_PATTERN = "^[0-9]+(.[0-9]{1,2})?$";

    public static final String VERIFICATION_CODE_PATTERN = "^[0-9]{6}$";
    /**
     * 校验运营管理员姓名
     * @param name
     * @return
     */
    public static Boolean regexOperatorName(String name){
        return Pattern.matches(OPERATOR_NAME_PATTERN, name);
    }
    /**
     * 校验合作伙伴名称
     * @param name
     * @return
     */
    public static Boolean regexPartnerName(String name){
        return Pattern.matches(PARTNER_NAME_PATTERN, name);
    }
    /**
     * 校验联系人姓名
     * @param name
     * @return
     */
    public static Boolean regexContactName(String name){
        return Pattern.matches(CONTACT_NAME_PATTERN, name);
    }
    /**
     * 校验手机号
     * @param phone
     * @return
     */
    public static Boolean regexPhone(String phone){
        return Pattern.matches(PHONE_PATTERN, phone);
    }
    /**
     * 校验邮箱地址
     * @param email
     * @return
     */
    public static Boolean regexEmail(String email){
        return Pattern.matches(EMAIL_PATTERN, email);
    }
    /**
     * 校验公司名
     * @param company
     * @return
     */
    public static Boolean regexCompany(String company){
        return Pattern.matches(COMPANY_PATTERN, company);
    }
    /**
     * 退换货联系人姓名
     * @param reContact
     * @return
     */
    public static Boolean regexReturnContact(String reContact){
        return Pattern.matches(RETURN_CONTACT_PATTERN, reContact);
    }

    /**
     * 短信验证码正则表达式
     * @param code
     * @return
     */
    public static Boolean regexVerificationCode(String code){
        return Pattern.matches(VERIFICATION_CODE_PATTERN, code);
    }

    /**
     * 校验是否最多2位小数的单价
     * @param price
     * @return
     */
    public static Boolean regexPriceDecimal(String price){
        return Pattern.matches(PRICE_DECIMAL_PATTERN, price);
    }

    /**
     * 校验是否为数字
     * @param num
     * @return
     */
    public static Boolean regexNumber(String num){
        return Pattern.matches(NUMBER_PATTERN, num);
    }

    public static void main(String[] args) {
        String num = "a1";
        System.out.println(regexOperatorName(num));
    }

}
