package com.chinamobile.iot.sc.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.chinamobile.iot.sc.common.Constant;

import java.util.Calendar;
import java.util.Date;
import java.util.UUID;

public class JWTUtil {


    private static Algorithm algorithm = Algorithm.HMAC256(Constant.ENCODE_PWD);

    private static JWTVerifier jwtVerifier = JWT.require(algorithm).build();

    /**
     * 对象转换为token
     * @param t
     * @param field
     * @param amount
     * @param <T>
     * @return
     */
    public static <T> String getToken(T t,Integer field,Integer amount,Long expireTime){

        String s = JSONObject.toJSONString(t);
        JWTCreator.Builder builder = JWT.create()
                .withClaim("id", UUID.randomUUID().toString().replace("-", ""))
                .withClaim("value", s);
        //设置过期时间
        if(expireTime != null){
            builder.withExpiresAt(new Date(expireTime));
        }else if(field != null && amount != null){
            Calendar calendar = Calendar.getInstance();
            calendar.add(field,amount);
            builder.withExpiresAt(calendar.getTime());
        }
        return builder.sign(algorithm);
    }

    /**
     * token转对象
     * @param token
     * @param t
     * @param <T>
     * @return
     */
    public static <T> T parseToken(String token,Class<T> t){
        String value = JWT.decode(token).getClaim("value").asString();
        return JSONObject.parseObject(value,t);
    }

    /**
     * 校验token
     * @param token
     * @return
     */
    public static Boolean verifierToken(String token){
        try {
            jwtVerifier.verify(token);
            return true;
        }catch (Exception e){
            return false;
        }
    }

    /**
     * token是否过期校验
     * @param token
     * @return
     */
    public static Boolean isExpired(String token){
        try {
            DecodedJWT verify = jwtVerifier.verify(token);
            Date date = verify.getExpiresAt();
            return date!=null && date.getTime()<System.currentTimeMillis();
        }catch (Exception e){
            return true;
        }
    }

}
