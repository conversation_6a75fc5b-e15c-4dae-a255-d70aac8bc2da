package com.chinamobile.iot.sc.mode;

import lombok.Data;

import java.util.Date;

/**
 * 开放能力应用主题内容配置
 *
 * <AUTHOR>
@Data
public class OpenAbilityThemeContentConfigRO {
    /**
     * 主键
     */
    private String id;

    /**
     * 能力开放部门ID
     */
    private String organizationId;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 内容ID
     */
    private String contentId;

    /**
     * 推送数据钩子
     */
    private String webHook;

    private String themeId;

    private String themeCode;

    /**
     * 类型：api-主动查询，subscribe-订阅
     *
     */
    private String type;

    /**
     * 接口名称/订阅数据名称
     */
    private String name;

    /**
     * 接口路径/订阅数据编码
     *
     */
    private String pathCode;

    /**
     * 接口详情页面url/订阅数据详情页面
     *
     */
    private String introUrl;

    /**
     * 接口类型
     */
    private String method;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}