package com.chinamobile.iot.sc.mode;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 开放能力应用配置
 *
 * <AUTHOR>
@Data
public class OpenAbilityAppRO {
    /**
     * 主键
     */
    private String id;

    /**
     * 机构ID
     */
    private String organizationId;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 应用所配置的字段列表
     */
    private List<OpenAbilityThemeFieldConfigRO> themeFieldConfigList;

    /**
     * 应用所配置的字段列表
     */
    private List<OpenAbilityThemeContentConfigRO> themeContentConfigList;

}