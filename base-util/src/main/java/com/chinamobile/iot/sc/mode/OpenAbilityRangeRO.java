package com.chinamobile.iot.sc.mode;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 开放能力枚举类型描述
 *
 * <AUTHOR>
@Data
public class OpenAbilityRangeRO {

    /**
     * 机构ID
     */
    private String name;

    /**
     * 可能是string，也可能是int
     */
    private Object code;

    public OpenAbilityRangeRO() {
    }

    public OpenAbilityRangeRO(String name, Object code) {
        this.name = name;
        this.code = code;
    }
}