#服务配置
server:
  port: 9292
  # 开启优雅下线
  shutdown: graceful
spring:
  profiles:
    active: test
  application:
    name: supply-chain-sms-svc77

#apollo配置
apollo:
#  meta: http://10.12.4.11:8080
  meta: http://10.12.57.1:8080
  bootstrap:
    enabled: true
    namespaces: application.yml
#apollo app.id
app:
  id: supply-chain-sms
# 暴露 shutdown和prometheus 接口
management:
  server:
    port: 8092
  endpoint:
    shutdown:
      enabled: true
    prometheus:
      enabled: true
  endpoints:
    web:
      exposure:
        include: '*'
  metrics:
    export:
      prometheus:
        enabled: true