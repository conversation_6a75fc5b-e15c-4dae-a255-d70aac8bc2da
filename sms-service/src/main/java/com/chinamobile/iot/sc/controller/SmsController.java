package com.chinamobile.iot.sc.controller;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.request.Msg4Request;
import com.chinamobile.iot.sc.request.response.Msg4Response;
import com.chinamobile.iot.sc.service.ISmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/8/13 11:05
 * @Description: 短信业务，分发为异步和同步接口，暂时只使用异步
 */
@RestController
@RequestMapping("/sms")
public class SmsController {
    private final ISmsService iSmsService;

    /**
     * 同步调用
     *
     * @param
     */
    @PostMapping("/synSendMessage")
    public BaseAnswer<List<Msg4Response>> synSendMessage(@Validated @RequestBody Msg4Request request) {
        return iSmsService.synSendMessage(request);
    }

    /**
     * 异步调用
     *
     * @param
     */
    @PostMapping("/asySendMessage")
    public BaseAnswer<Void> asySendMessage(@Validated @RequestBody Msg4Request request) {
        return iSmsService.asySendMessage(request);
    }

    @Autowired
    public SmsController(ISmsService iSmsService) {
        this.iSmsService = iSmsService;
    }
}
