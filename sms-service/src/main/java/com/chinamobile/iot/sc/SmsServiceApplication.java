package com.chinamobile.iot.sc;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableApolloConfig
@SpringBootApplication
@EnableDiscoveryClient //范围注册与发现
@EnableFeignClients //开启feign
public class SmsServiceApplication {
    public static void main( String[] args ) {
        SpringApplication.run(SmsServiceApplication.class,args);
    }
}
