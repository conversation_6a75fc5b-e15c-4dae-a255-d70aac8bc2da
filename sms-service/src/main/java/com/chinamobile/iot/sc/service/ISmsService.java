package com.chinamobile.iot.sc.service;


import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.request.Msg4Request;
import com.chinamobile.iot.sc.request.response.Msg4Response;

import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/8/13 11:06
 * @Description:
 */

public interface ISmsService {
    /**
     * 同步发送消息
     */
    BaseAnswer<List<Msg4Response>> synSendMessage(Msg4Request msg4Request);

    /**
     * 异步发送消息，直接返回成功
     */
    BaseAnswer<Void> asySendMessage(Msg4Request msg4Request);
}
