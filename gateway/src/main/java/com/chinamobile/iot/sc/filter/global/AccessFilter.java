package com.chinamobile.iot.sc.filter.global;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.iot.sc.common.utils.JWTUtil;
import com.chinamobile.iot.sc.config.AccessConfig;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.mode.*;
import com.chinamobile.iot.sc.service.OpenAbilityService;
import com.chinamobile.iot.sc.service.PermissionService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @package: com.chinamobile.iot.sc.filter.global
 * @ClassName: AccessFilter
 * @description: 网关权限校验
 * @author: zyj
 * @create: 2021/11/9 15:18
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
@Component
public class AccessFilter implements GlobalFilter, Ordered {
    @Resource
    private AccessConfig accessConfig;
    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private PermissionService permissionService;

    @Resource
    private OpenAbilityService openAbilityService;


    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 请求对象
        ServerHttpRequest request = exchange.getRequest();
        // 响应对象
        ServerHttpResponse response = exchange.getResponse();
        try {
            //生成traceId
            String traceId = BaseUtils.uuid();
            String ip = getCertainIP(request);
            String token = request.getHeaders().toSingleValueMap().get(Constant.HEADER_KEY_TOKEN);
            //检查请求的连接，是否是直接通过的连接
            String path = request.getURI().getPath();
            log.info("ip:{},path:{}",ip,path);

//            //test 打印请求
//            // 头信息打印（过滤敏感头）
//            request.getHeaders().forEach((name, values) -> {
//                if (!name.equalsIgnoreCase("Authorization")) {
//                    log.info("Header '{}' => {}", name, values);
//                }
//            });
//            //test end

            //禁止调用内部接口
            if(path.contains("internalAPI")){
                log.info("禁止调用内部接口,path:{}",path);
                return response(response, HttpStatus.UNAUTHORIZED, BaseErrorConstant.UN_PERMISSION,null);
            }
            if(checkIgnore(path,request.getMethodValue())){
                log.info("该链接不进行验证：{}",path);
                //直接通过
                Consumer<HttpHeaders> httpHeaders = httpHeader -> {
                    try {
                        httpHeader.set(Constant.TRACE_ID, traceId);
                        httpHeader.set(Constant.IP, ip);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                };
                request.mutate().headers(httpHeaders).build();
                return chain.filter(exchange.mutate().request(request).build());
            } else if (openAbilityService.checkOpenApi(path,request.getMethodValue())) {
                log.info("开放能力API：{}",path);
                String appId = request.getHeaders().toSingleValueMap().get(Constant.HEADER_KEY_OPEN_APP_ID);
                String timestampStr = request.getHeaders().toSingleValueMap().get(Constant.HEADER_KEY_TIMESTAMP);
                String sign = request.getHeaders().toSingleValueMap().get(Constant.HEADER_KEY_SIGN);
                String rand = request.getHeaders().toSingleValueMap().get(Constant.HEADER_KEY_RAND);
                if(StringUtils.isBlank(appId) || StringUtils.isBlank(timestampStr)
                        || StringUtils.isBlank(rand) || StringUtils.isBlank(sign)){
                    return response(response, HttpStatus.UNAUTHORIZED, BaseErrorConstant.OPEN_ABILITY_SIGN_ARGUMENT_ERROR,null);
                }
                OpenAbilityAppRO openAbilityAppRO = openAbilityService.getOpenApp(appId);
                if(ObjectUtils.isEmpty(openAbilityAppRO)){
                    return response(response, HttpStatus.UNAUTHORIZED, BaseErrorConstant.OPEN_ABILITY_NO_APP,null);
                }
                List<String> apis = openAbilityAppRO.getThemeContentConfigList().stream()
                        .filter(x->StringUtils.equals(x.getType(),BaseConstant.OPEN_ABILITY_CONTENT_TPE_API))
                        .map(x->x.getPathCode() + "_" + x.getMethod()).collect(Collectors.toList());
                if (ObjectUtils.isEmpty(apis) || apis.stream().noneMatch(
                        x -> StringUtils.equals(openAbilityService.getDatabaseUri(path) + "_" + StringUtils.lowerCase(request.getMethodValue()),x) )) {
                    return response(response, HttpStatus.UNAUTHORIZED, BaseErrorConstant.OPEN_ABILITY_NOT_ALLOWED,null);
                }
                long timestamp = Long.parseLong(timestampStr);
                long current = System.currentTimeMillis();
                log.info("open ability timestamp:{},current:{}",timestamp,current);
                if (Math.abs(current - timestamp) > 15000) {
                    return response(response, HttpStatus.UNAUTHORIZED, BaseErrorConstant.OPEN_ABILITY_TIME_OUT,null);
                }
                OpenAbilityOrganizationRO organizationRO = openAbilityService.getOpenOrganization(openAbilityAppRO.getOrganizationId());
                if(ObjectUtils.isEmpty(organizationRO)){
                    return response(response, HttpStatus.UNAUTHORIZED, BaseErrorConstant.OPEN_ABILITY_ORGANIZATION_ERROR,null);
                }
                log.info("third sign = {}",BaseUtils.getOpenSign(appId,rand,timestampStr,organizationRO.getApiKey()));
                if (!StringUtils.equals(BaseUtils.getOpenSign(appId,rand,timestampStr,organizationRO.getApiKey()),sign)) {
                    return response(response, HttpStatus.UNAUTHORIZED, BaseErrorConstant.OPEN_ABILITY_SIGN_ERROR,null);
                }
                Consumer<HttpHeaders> httpHeaders = httpHeader -> {
                    try {
                        httpHeader.set(Constant.TRACE_ID, traceId);
                        httpHeader.set(Constant.IP, ip);
                        httpHeader.set(Constant.HEADER_KEY_OPEN_APP_ID,appId);
                        httpHeader.set(Constant.HEADER_KEY_OPEN_ORGANIZATION_ID,organizationRO.getId());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                };
                request.mutate().headers(httpHeaders).build();
                return chain.filter(exchange.mutate().request(request).build());
            }

            //删除header中用户id传参
            request.mutate().headers(k -> k.remove(Constant.HEADER_KEY_USER_ID));
            // 通过token进行校验
            if(StringUtils.isBlank(token) || !JWTUtil.verifierToken(token)){
                log.info("token为空或者token校验失败,token:{}",token);
                return response(response,HttpStatus.UNAUTHORIZED, BaseErrorConstant.UN_PERMISSION,"token为空或者token校验失败");
            }
            AccessToken accessToken = JWTUtil.parseToken(token, AccessToken.class);
            String userId = accessToken.getUserId();
            //获取用户基本信息
            LoginIfo4Redis loginIfo4Redis = (LoginIfo4Redis)redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + userId);
            if(loginIfo4Redis == null || !token.equals(loginIfo4Redis.getToken())){
                log.info("token已失效,token:{}",token);
                return response(response,HttpStatus.UNAUTHORIZED, BaseErrorConstant.UN_PERMISSION,"token已失效，请重新登录");
            }

            Map<String,String> consumerHeaderMap = new LinkedHashMap<>();
            consumerHeaderMap.put(Constant.TRACE_ID, traceId);
            consumerHeaderMap.put(Constant.HEADER_KEY_USER_ID, accessToken.getUserId());
            consumerHeaderMap.put(Constant.IP, ip);

//            if(path.contains("order/configed/areaList")){
//                //调试大屏导出地区选择接口原始请求
//                log.info("调试大屏导出地区选择接口原始请求 screenProvince_token:{}",token);
//                log.info("DataScreen Error userId = {}",accessToken.getUserId());
//                log.info("DataScreen gateway loginRedisInfo：{}",loginIfo4Redis);
//            }


            //进行权限校验
            List<OperatePermissionRO> uriOperatePermissionROS = permissionService.getOperatePermissionsByUri(
                    permissionService.getDatabaseUri(request.getURI().getPath()), request.getMethodValue());
            if (CollectionUtils.isNotEmpty(uriOperatePermissionROS)) {
                //用户当前角色所有持有的权限集
                List<RoleOperatePermissionRO> roleOperatePermissionROS = permissionService.getRoleOperatePermissionsByRole(loginIfo4Redis.getRoleId());
                if (CollectionUtils.isEmpty(roleOperatePermissionROS)) {
                    //角色没有任何操作权限
                    return response(response, HttpStatus.UNAUTHORIZED,BaseErrorConstant.UN_PERMISSION, BaseErrorConstant.UN_PERMISSION.getMessage());
                }
                List<String> roleOperatePermissionIds = roleOperatePermissionROS.stream()
                        .map(RoleOperatePermissionRO::getOperatePermissionId).collect(Collectors.toList());
                List<OperatePermissionRO> operatePermissionROList = uriOperatePermissionROS.stream().filter(permission ->
                        roleOperatePermissionIds.contains(permission.getId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(operatePermissionROList)) {
                    //角色持有权限，把角色持有的权限对应的authCode保存在上下文中，方便后续操作权限以及数据权限的校验
                    //要求同一接口的所有不同authCode处于同一模块，即parentId相同，且不为-1，
                    //过滤掉超管特有权限，如果接口只有超管特有权限,则不需要往attr里面添加code
                    List<String> funcAuthCodes = operatePermissionROList.stream().map(OperatePermissionRO::getAuthCode)
                            .filter(code -> !StringUtils.equals(code,BaseConstant.PERMISSION_SUPER_ADMIN_ONLY))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(funcAuthCodes)) {
                        AuthRO auth = permissionService.getAuthByAuthCode(funcAuthCodes.get(0));
                        if (auth == null) {
                            //权限码异常，不能再redis里面找到
                            return response(response, HttpStatus.UNAUTHORIZED, BaseErrorConstant.UN_PERMISSION, BaseErrorConstant.UN_PERMISSION.getMessage());
                        }

                        if ("-1".equals(auth.getParentId())) {
                            //父模块ID为-1则表示当前auth为top module
                            //写入用户信息到header中,由于部分信息是中文，所以部分header为乱码，需要解码
                            consumerHeaderMap.put(Constant.ATTR_KEY_TOP_MODULE_AUTH_CODE, funcAuthCodes.get(0));

                        } else {
                            //获取auth的父auth
                            AuthRO parentAuth = permissionService.getAuthByAuthId(auth.getParentId());
                            if (parentAuth == null) {
                                return response(response, HttpStatus.UNAUTHORIZED, BaseErrorConstant.UN_PERMISSION, BaseErrorConstant.UN_PERMISSION.getMessage());
                            }
                            if ("-1".equals(parentAuth.getParentId())) {
                                //父auth的parentID为-1，则表示父auth为top module
                                consumerHeaderMap.put(Constant.ATTR_KEY_TOP_MODULE_AUTH_CODE, parentAuth.getAuthCode());
                                consumerHeaderMap.put(Constant.ATTR_KEY_SUB_MODULE_AUTH_CODE, funcAuthCodes.get(0));
                            } else {
                                //获取父auth的父auth，系统中最多三级
                                AuthRO parentParentAuth = permissionService.getAuthByAuthId(parentAuth.getParentId());
                                if (parentParentAuth == null) {
                                    return response(response, HttpStatus.UNAUTHORIZED, BaseErrorConstant.UN_PERMISSION, BaseErrorConstant.UN_PERMISSION.getMessage());
                                }
                                consumerHeaderMap.put(Constant.ATTR_KEY_TOP_MODULE_AUTH_CODE, parentParentAuth.getAuthCode());
                                consumerHeaderMap.put(Constant.ATTR_KEY_SUB_MODULE_AUTH_CODE, parentAuth.getAuthCode());
                                consumerHeaderMap.put(Constant.ATTR_KEY_FUNCTION_MODULE_AUTH_CODE, JSON.toJSONString(funcAuthCodes));
                            }
                        }
                    }
                } else {
                    //角色没有持有接口的操作权限
                    return response(response, HttpStatus.UNAUTHORIZED,BaseErrorConstant.UN_PERMISSION, BaseErrorConstant.UN_PERMISSION.getMessage());
                }
            }

            Consumer<HttpHeaders> httpHeaders = httpHeader -> {
                try {
                    consumerHeaderMap.forEach(httpHeader::set);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            };
            request.mutate().headers(httpHeaders).build();

        }catch(Exception e){
            log.error("发生未知错误",e);
            return response(response,HttpStatus.INTERNAL_SERVER_ERROR, BaseErrorConstant.SERVER_INTERNAL_ERROR,null);
        }
        //转发的对应服务
        return chain.filter(exchange.mutate().request(request).build());
    }

    @Override
    public int getOrder() {
        return -1;
    }

    private boolean checkIgnore(String uri, String md) {
        List<String> ignoreUriList = accessConfig.getIgnoreUri();
        if(ignoreUriList != null && ignoreUriList.size() > 0){
            for(String ignoreUri : ignoreUriList){
                String[] ss = ignoreUri.split(":");
                String newPattern = ss[1].replace("*", ".*");//*:/v1/basic/captcha/*
                Pattern ptn = Pattern.compile(newPattern);
                String method = ss[0].toLowerCase();
                Matcher matcher = ptn.matcher(uri);
                if((matcher.matches() && (method.equals("*") || method.equals(md.toLowerCase())))){
                    return true;
                }
            }
        }
        return false;
    }

    private Mono<Void> response(ServerHttpResponse response, HttpStatus httpStatus, ExcepStatus excepStatus, String message){
        BaseAnswer answer = new BaseAnswer(excepStatus);
        if (StringUtils.isNotBlank(message)) {
            answer.setMessage(message);
        }
        return response(response,httpStatus,answer);
    }

    private Mono<Void> response(ServerHttpResponse response, HttpStatus httpStatus, BaseAnswer answer){
        response.setStatusCode(httpStatus);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        String result = "";
        if(answer != null){
            ObjectMapper mapper = new ObjectMapper();
            try {
                result = mapper.writeValueAsString(answer);
            }catch (Exception e){
                log.error("json转换失败",e);
            }
        }
        byte[] bytes = result.getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = response.bufferFactory().wrap(bytes);
        return response.writeWith(Flux.just(buffer));
    }

    private String getCertainIP(ServerHttpRequest request){
        String realIp = request.getHeaders().getFirst(Constant.X_Real_IP);
        String forward = request.getHeaders().getFirst(Constant.X_Forwarded_For);
        String remoteAddress = "";
        InetSocketAddress remoteAddress1 = request.getRemoteAddress();
        log.info("InetSocketAddress:{}",remoteAddress1);
        if(remoteAddress1 != null){
            InetAddress address = remoteAddress1.getAddress();
            log.info("InetAddress:{}",address);
            if(address != null){
                remoteAddress = address.getHostAddress();
                log.info("remoteAddress:{}",remoteAddress);

            }
        }

        /**
         * 获取真实地址：
         * 1. 获取X-Forwarded-For中，第一个ip
         * 2. 若X-Forwarded-For无ip，则获取X-Real-IP
         * 3. 若X-Real-IP无pid，则获取remoteAddress中ip
         */
        if(!ObjectUtils.isEmpty(forward) && isIp(forward.split(",")[0])){
            realIp = forward.split(",")[0].trim();
        }else if(!ObjectUtils.isEmpty(realIp) && isIp(realIp)){

        }else if(ObjectUtils.isEmpty(realIp)){
            realIp = remoteAddress;
        }
        return realIp;
    }

    /** * 判断是否为合法IP * @return the ip */
    public static boolean isIp(String ipAddress) {
        String ip = "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}";
        Pattern pattern = Pattern.compile(ip);
        Matcher matcher = pattern.matcher(ipAddress);
        return matcher.matches();
    }
}
