package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.AuthRO;
import com.chinamobile.iot.sc.mode.OperatePermissionRO;
import com.chinamobile.iot.sc.mode.RoleOperatePermissionRO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 权限相关查询
 * */
@Service
@Slf4j
public class PermissionService {
    @Resource
    private RedisTemplate redisTemplate;

    public String getDatabaseUri(String requestUri) {
        String pref = "/mallos/api";
        if (requestUri.startsWith(pref)) {
            return requestUri.substring(pref.length());
        } else {
            return requestUri;
        }
    }

    public List<RoleOperatePermissionRO> getRoleOperatePermissionsByRole(String roleId) {
        String roleIdRedisKey = Constant.REDIS_KEY_ROLE_OPERATE_PERMISSION + roleId;
        List<RoleOperatePermissionRO> roleOperatePermissionROS = (List<RoleOperatePermissionRO>) redisTemplate.opsForValue().get(roleIdRedisKey);
        return roleOperatePermissionROS;
    }

    public List<OperatePermissionRO> getOperatePermissionsByUri(String uri, String method) {
        String permissionRedisKey = Constant.REDIS_KEY_OPERATE_PERMISSION + uri + method.toLowerCase();
        List<OperatePermissionRO> uriOperatePermissionROS = (List<OperatePermissionRO>) redisTemplate.opsForValue().get(permissionRedisKey);
        return uriOperatePermissionROS;
    }

    public OperatePermissionRO getOperatePermissionByUriAndRole(String roleId, String uri, String method) {
        List<String> roleHasPermissions = getRoleOperatePermissionsByRole(roleId).stream()
                .map(RoleOperatePermissionRO::getOperatePermissionId).collect(Collectors.toList());
        List<OperatePermissionRO> uriPermissions = getOperatePermissionsByUri(uri, method);
        OperatePermissionRO permission = null;
        if (CollectionUtils.isNotEmpty(roleHasPermissions) && CollectionUtils.isNotEmpty(uriPermissions)) {
            Collection<String> ids = CollectionUtils.intersection(roleHasPermissions,
                    uriPermissions.stream().map(OperatePermissionRO::getId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(ids)) {
                String id = ids.iterator().next();
                Optional<OperatePermissionRO> first = uriPermissions.stream().filter(item -> item.getId().equals(id)).findFirst();
                if (first.isPresent()) {
                    permission= first.get();
                }
            }
        }

        return permission;
    }

    public AuthRO getAuthByAuthCode(String authCode) {
        String authCodeRedisKey = Constant.REDIS_KEY_AUTH_CODE + authCode;
        AuthRO auth = (AuthRO) redisTemplate.opsForValue().get(authCodeRedisKey);
        return auth;
    }

    public AuthRO getAuthByAuthId(String authId) {
        if ("-1".equals(authId)) {
            return null;
        }
        String authIdRedisKey = Constant.REDIS_KEY_AUTH_ID + authId;
        AuthRO auth = (AuthRO) redisTemplate.opsForValue().get(authIdRedisKey);
        return auth;
    }

}
