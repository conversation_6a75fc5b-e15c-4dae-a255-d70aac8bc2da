package com.chinamobile.iot.sc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 鉴权相关配置
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix="access-config")
@Configuration
@RefreshScope
public class AccessConfig {

    /**
     * 不需要进行鉴权的uri
     */
    private List<String> ignoreUri;



}
