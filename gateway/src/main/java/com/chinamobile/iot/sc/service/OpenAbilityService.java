package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 权限相关查询
 * */
@Service
@Slf4j
public class OpenAbilityService {
    @Resource
    private RedisTemplate redisTemplate;

    public String getDatabaseUri(String requestUri) {
        String pref = "/mallos/api";
        if (requestUri.startsWith(pref)) {
            return requestUri.substring(pref.length());
        } else {
            return requestUri;
        }
    }

    public boolean checkOpenApi(String requestUri, String md) {
        List<String> openApiList = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_OPEN_ABILITY_API_LIST);
        if(CollectionUtils.isNotEmpty(openApiList)){
            String key = getDatabaseUri(requestUri)+ "_" + StringUtils.lowerCase(md);
            return openApiList.stream().anyMatch(s -> StringUtils.equals(s,key));
        }
        return false;
    }

    public OpenAbilityAppRO getOpenApp(String appId) {
        String appIdRedisKey = Constant.REDIS_KEY_OPEN_ABILITY_APP_ID + appId;
        OpenAbilityAppRO openAbilityAppRO = (OpenAbilityAppRO) redisTemplate.opsForValue().get(appIdRedisKey);
        return openAbilityAppRO;
    }

    public OpenAbilityOrganizationRO getOpenOrganization(String organizationId) {
        String organizationRedisKey = Constant.REDIS_KEY_OPEN_ABILITY_ORGANIZATION_ID + organizationId;
        OpenAbilityOrganizationRO openAbilityAppRO = (OpenAbilityOrganizationRO) redisTemplate.opsForValue().get(organizationRedisKey);
        return openAbilityAppRO;
    }

}
