package com.chinamobile.install.dao;

import com.chinamobile.install.pojo.entity.UserPartner;
import com.chinamobile.install.pojo.entity.UserPartnerExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserPartnerMapper {
    /**
     *
     * @mbg.generated Mon May 26 16:36:50 CST 2025
     */
    long countByExample(UserPartnerExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:50 CST 2025
     */
    int deleteByExample(UserPartnerExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:50 CST 2025
     */
    int deleteByPrimaryKey(String userId);

    /**
     *
     * @mbg.generated Mon May 26 16:36:50 CST 2025
     */
    int insert(UserPartner record);

    /**
     *
     * @mbg.generated Mon May 26 16:36:50 CST 2025
     */
    int insertSelective(UserPartner record);

    /**
     *
     * @mbg.generated Mon May 26 16:36:50 CST 2025
     */
    List<UserPartner> selectByExample(UserPartnerExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:50 CST 2025
     */
    UserPartner selectByPrimaryKey(String userId);

    /**
     *
     * @mbg.generated Mon May 26 16:36:50 CST 2025
     */
    int updateByExampleSelective(@Param("record") UserPartner record, @Param("example") UserPartnerExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:50 CST 2025
     */
    int updateByExample(@Param("record") UserPartner record, @Param("example") UserPartnerExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:50 CST 2025
     */
    int updateByPrimaryKeySelective(UserPartner record);

    /**
     *
     * @mbg.generated Mon May 26 16:36:50 CST 2025
     */
    int updateByPrimaryKey(UserPartner record);

    /**
     *
     * @mbg.generated Mon May 26 16:36:50 CST 2025
     */
    int batchInsert(@Param("list") List<UserPartner> list);

    /**
     *
     * @mbg.generated Mon May 26 16:36:50 CST 2025
     */
    int batchInsertSelective(@Param("list") List<UserPartner> list, @Param("selective") UserPartner.Column ... selective);
}