package com.chinamobile.install.dao;

import com.chinamobile.install.pojo.entity.Order2cInfo;
import com.chinamobile.install.pojo.entity.Order2cInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface Order2cInfoMapper {
    /**
     *
     * @mbg.generated Fri May 23 17:13:25 CST 2025
     */
    long countByExample(Order2cInfoExample example);

    /**
     *
     * @mbg.generated Fri May 23 17:13:25 CST 2025
     */
    int deleteByExample(Order2cInfoExample example);

    /**
     *
     * @mbg.generated Fri May 23 17:13:25 CST 2025
     */
    int deleteByPrimaryKey(String orderId);

    /**
     *
     * @mbg.generated Fri May 23 17:13:25 CST 2025
     */
    int insert(Order2cInfo record);

    /**
     *
     * @mbg.generated Fri May 23 17:13:25 CST 2025
     */
    int insertSelective(Order2cInfo record);

    /**
     *
     * @mbg.generated Fri May 23 17:13:25 CST 2025
     */
    List<Order2cInfo> selectByExample(Order2cInfoExample example);

    /**
     *
     * @mbg.generated Fri May 23 17:13:25 CST 2025
     */
    Order2cInfo selectByPrimaryKey(String orderId);

    /**
     *
     * @mbg.generated Fri May 23 17:13:25 CST 2025
     */
    int updateByExampleSelective(@Param("record") Order2cInfo record, @Param("example") Order2cInfoExample example);

    /**
     *
     * @mbg.generated Fri May 23 17:13:25 CST 2025
     */
    int updateByExample(@Param("record") Order2cInfo record, @Param("example") Order2cInfoExample example);

    /**
     *
     * @mbg.generated Fri May 23 17:13:25 CST 2025
     */
    int updateByPrimaryKeySelective(Order2cInfo record);

    /**
     *
     * @mbg.generated Fri May 23 17:13:25 CST 2025
     */
    int updateByPrimaryKey(Order2cInfo record);

    /**
     *
     * @mbg.generated Fri May 23 17:13:25 CST 2025
     */
    int batchInsert(@Param("list") List<Order2cInfo> list);

    /**
     *
     * @mbg.generated Fri May 23 17:13:25 CST 2025
     */
    int batchInsertSelective(@Param("list") List<Order2cInfo> list, @Param("selective") Order2cInfo.Column ... selective);
}