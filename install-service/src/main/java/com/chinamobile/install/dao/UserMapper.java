package com.chinamobile.install.dao;

import com.chinamobile.install.pojo.entity.User;
import com.chinamobile.install.pojo.entity.UserExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserMapper {
    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    long countByExample(UserExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    int deleteByExample(UserExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    int deleteByPrimaryKey(String userId);

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    int insert(User record);

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    int insertSelective(User record);

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    List<User> selectByExample(UserExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    User selectByPrimaryKey(String userId);

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    int updateByExampleSelective(@Param("record") User record, @Param("example") UserExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    int updateByExample(@Param("record") User record, @Param("example") UserExample example);

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    int updateByPrimaryKeySelective(User record);

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    int updateByPrimaryKey(User record);

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    int batchInsert(@Param("list") List<User> list);

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    int batchInsertSelective(@Param("list") List<User> list, @Param("selective") User.Column ... selective);
}