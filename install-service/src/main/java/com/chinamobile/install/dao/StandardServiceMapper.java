package com.chinamobile.install.dao;

import com.chinamobile.install.pojo.entity.StandardService;
import com.chinamobile.install.pojo.entity.StandardServiceExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StandardServiceMapper {
    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    long countByExample(StandardServiceExample example);

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    int deleteByExample(StandardServiceExample example);

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    int insert(StandardService record);

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    int insertSelective(StandardService record);

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    List<StandardService> selectByExample(StandardServiceExample example);

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    StandardService selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    int updateByExampleSelective(@Param("record") StandardService record, @Param("example") StandardServiceExample example);

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    int updateByExample(@Param("record") StandardService record, @Param("example") StandardServiceExample example);

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    int updateByPrimaryKeySelective(StandardService record);

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    int updateByPrimaryKey(StandardService record);

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    int batchInsert(@Param("list") List<StandardService> list);

    /**
     *
     * @mbg.generated Tue May 27 10:02:20 CST 2025
     */
    int batchInsertSelective(@Param("list") List<StandardService> list, @Param("selective") StandardService.Column ... selective);
}