package com.chinamobile.install.dao;

import com.chinamobile.install.pojo.entity.Order2cAgentInfo;
import com.chinamobile.install.pojo.entity.Order2cAgentInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface Order2cAgentInfoMapper {
    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    long countByExample(Order2cAgentInfoExample example);

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    int deleteByExample(Order2cAgentInfoExample example);

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    int insert(Order2cAgentInfo record);

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    int insertSelective(Order2cAgentInfo record);

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    List<Order2cAgentInfo> selectByExample(Order2cAgentInfoExample example);

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    Order2cAgentInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    int updateByExampleSelective(@Param("record") Order2cAgentInfo record, @Param("example") Order2cAgentInfoExample example);

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    int updateByExample(@Param("record") Order2cAgentInfo record, @Param("example") Order2cAgentInfoExample example);

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    int updateByPrimaryKeySelective(Order2cAgentInfo record);

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    int updateByPrimaryKey(Order2cAgentInfo record);

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    int batchInsert(@Param("list") List<Order2cAgentInfo> list);

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    int batchInsertSelective(@Param("list") List<Order2cAgentInfo> list, @Param("selective") Order2cAgentInfo.Column ... selective);
}