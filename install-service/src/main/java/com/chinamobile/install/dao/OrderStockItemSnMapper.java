package com.chinamobile.install.dao;

import com.chinamobile.install.pojo.entity.OrderStockItemSn;
import com.chinamobile.install.pojo.entity.OrderStockItemSnExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OrderStockItemSnMapper {
    long countByExample(OrderStockItemSnExample example);

    int deleteByExample(OrderStockItemSnExample example);

    int deleteByPrimaryKey(String id);

    int insert(OrderStockItemSn record);

    int insertSelective(OrderStockItemSn record);

    List<OrderStockItemSn> selectByExample(OrderStockItemSnExample example);

    OrderStockItemSn selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") OrderStockItemSn record, @Param("example") OrderStockItemSnExample example);

    int updateByExample(@Param("record") OrderStockItemSn record, @Param("example") OrderStockItemSnExample example);

    int updateByPrimaryKeySelective(OrderStockItemSn record);

    int updateByPrimaryKey(OrderStockItemSn record);

    int batchInsert(@Param("list") List<OrderStockItemSn> list);

    int batchInsertSelective(@Param("list") List<OrderStockItemSn> list, @Param("selective") OrderStockItemSn.Column ... selective);
}