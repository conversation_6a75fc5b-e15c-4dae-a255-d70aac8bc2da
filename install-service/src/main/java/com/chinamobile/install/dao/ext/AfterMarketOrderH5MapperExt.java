package com.chinamobile.install.dao.ext;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.install.pojo.mapper.AfterMarketOrderH5DO;
import com.chinamobile.install.pojo.param.AfterMarketOrderH5QueryParam;
import com.chinamobile.install.pojo.vo.AfterMarketOrderH5ListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/14
 * @description 订单售后服务H5mapper类
 */
public interface AfterMarketOrderH5MapperExt {

    /**
     * 获取订单售后服务列表
     *
     * @param page
     * @param userId
     * @return
     */
    List<AfterMarketOrderH5ListVO> listAfterMarketOrderH5(@Param("page") Page page, String userId);

    /**
     * 获取售后订单详情
     *
     * @param afterMarketOrderH5QueryParam
     * @return
     */
    AfterMarketOrderH5DO getAfterMarketOrderH5(@Param("afterMarketOrderH5QueryParam") AfterMarketOrderH5QueryParam afterMarketOrderH5QueryParam);
}
