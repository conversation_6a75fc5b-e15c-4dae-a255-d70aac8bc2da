package com.chinamobile.iot.sc;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.retry.annotation.EnableRetry;

@EnableRetry
@EnableDiscoveryClient
@EnableApolloConfig
@EnableFeignClients
@SpringBootApplication
public class EStoreApplication {
    public static void main(String[] args) {
        SpringApplication.run(EStoreApplication.class,args);
    }
}
