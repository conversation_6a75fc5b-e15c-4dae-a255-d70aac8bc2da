package com.chinamobile.iot.sc.constant;

/**
 * @package: com.chinamobile.iot.sc.constant
 * @ClassName: FNetOrderStatusEnum
 * @description: 外部电商订单交易状态枚举
 * @author: zyj
 * @create: 2022/2/14 16:56
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public enum FNetOrderStatusEnum {

    TRADE_NO_CREATE_PAY(1,"异常", "TRADE_NO_CREATE_PAY"),
    TRADE_WAIT_BUYER_PAY(2,"待付款", "TRADE_WAIT_BUYER_PAY"),
    TRADE_SELLER_SEND_GOODS(3,"待发货", "TRADE_SELLER_SEND_GOODS"),
    TRADE_SELLER_CONSIGNED_PART(4,"已部分发货", "TRADE_SELLER_CONSIGNED_PART"),
    TRADE_WAIT_BUYER_CONFIRM_GOODS(5,"待签收", "TRADE_WAIT_BUYER_CONFIRM_GOODS"),
    TRADE_BUYER_SIGNED(6,"已签收待付款","TRADE_BUYER_SIGNED"),
    TRADE_AUTOMATIC_CLOSED(7,"自动关闭","TRADE_AUTOMATIC_CLOSED"),
    TRADE_FINISHED(8,"交易成功","TRADE_FINISHED"),
    TRADE_SEND_TO_DISTRIBUTION_CENER(9,"发往配送中心","TRADE_SEND_TO_DISTRIBUTION_CENER"),
    TRADE_DISTRIBUTION_CENTER_RECEIVED(10,"配送中心已收货","TRADE_DISTRIBUTION_CENTER_RECEIVED"),
    TRADE_RECEIPTS_CONFIRM(11,"收款确认","TRADE_RECEIPTS_CONFIRM"),
    TRADE_LOCKED(12,"已锁定","TRADE_LOCKED"),
    TRADE_CLOSED(13,"关闭","TRADE_CLOSED"),
    TRADE_REFUNDING(14,"退款中","TRADE_REFUNDING")
    ;
    /**
     * 序号
     */
    private Integer no;
    /**
     * 状态描述
     */
    private String description;
    /**
     * 状态代码
     */
    private String code;

    FNetOrderStatusEnum(Integer no, String description, String code) {
        this.no = no;
        this.description = description;
        this.code = code;
    }

    public Integer getNo() {
        return no;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return code;
    }

    /**
     * 获取交易状态码对应描述信息
     * @param code
     * @return
     */
    public static String getCodeDescribe(String code){
        for(FNetOrderStatusEnum val : FNetOrderStatusEnum.values()){
            if(val.getCode().equals(code)){
                return val.getDescription();
            }
        }
        return null;
    }
}
