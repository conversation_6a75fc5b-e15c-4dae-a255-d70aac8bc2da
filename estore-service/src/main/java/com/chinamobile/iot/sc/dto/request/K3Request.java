package com.chinamobile.iot.sc.dto.request;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @package: com.chinamobile.iot.sc.dto.request
 * @ClassName: K3Request
 * @description: 对接K3系统查询对象
 * @author: zyj
 * @create: 2022/2/15 15:25
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@Accessors(chain = true)
public class K3Request {
    /**
     * 业务对象表单Id
     */
    private String FormId;
    /**
     * 需查询的字段key集合，字符串类型，格式：'key1, key2, ...（必录）
     */
    private String FieldKeys;
    /**
     * 过滤条件（非必录）
     */
    private String FilterString;
    /**
     * 排序字段（非必录）
     */
    private String OrderString;
    /**
     * 返回总行数，整型（非必录）
     */
    private Integer TopRowCount=1000000;
    /**
     * 开始行索引，整型（非必录）
     */
    private Integer StartRow=0;
    /**
     * 最大行数，整型，不能超过2000（非必录）
     */
    private Integer Limit=10;
}
