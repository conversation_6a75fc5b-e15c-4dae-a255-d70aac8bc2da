package com.chinamobile.iot.sc.dto.request;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @package: com.chinamobile.iot.sc.dto.request
 * @ClassName: EStore4PageRequest
 * @description: 外部电商订单信息分页查询请求
 * @author: zyj
 * @create: 2022/2/15 14:18
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@Accessors(chain = true)
public class EStore4PageRequest extends BasePageQuery {
    /**
     * 商品名称、订单号、收货省模糊查询
     */
    private String goodOrderProvince;
    /**
     * 订单状态
     */
    private String status;

}
