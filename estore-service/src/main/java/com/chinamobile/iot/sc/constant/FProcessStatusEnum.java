package com.chinamobile.iot.sc.constant;

/**
 * @package: com.chinamobile.iot.sc.constant
 * @ClassName: FProcessStatusEnum
 * @description: 下线处理状态枚举
 * @author: zyj
 * @create: 2022/2/15 13:49
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public enum FProcessStatusEnum {
    HANDLE_UNPROCESS(1, "未开始处理", "1"),
    HANDLE_PREPARE_CHECK(2,"审单准备","2"),
    HANDLE_WAIT_CHECK(3,"待审单","3"),
    HANDLE_WAIT_PICK(4,"待拣货","4"),
    HANDLE_WAIT_INSPECT(5,"待验货","5"),
    HANDLE_WAIT_WEIGHT(6,"待称重","6"),
    HANDLE_WAIT_DELIVER(7,"待发货","7"),
    HANDLE_FINISH_DELIVER(8,"发货完成","8"),
    HANDLE_NO_NEED(9,"无需处理","9"),
    ;

    private Integer no;
    private String description;
    private String code;

    FProcessStatusEnum(Integer no, String description, String code) {
        this.no = no;
        this.description = description;
        this.code = code;
    }

    public Integer getNo() {
        return no;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return code;
    }

    public static String getCodeDescribe(String code){
        for(FProcessStatusEnum val : FProcessStatusEnum.values()){
            if(val.code.equals(code)){
                return val.getDescription();
            }
        }
        return null;
    }
}
