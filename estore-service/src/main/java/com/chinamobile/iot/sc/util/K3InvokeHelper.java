package com.chinamobile.iot.sc.util;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;


@Component
public class K3InvokeHelper {
	private static Logger log = LoggerFactory.getLogger(K3InvokeHelper.class);

	// K3 Cloud WebSite URL Example "http://192.168.19.113/K3Cloud/" http://172.18.0.61/K3Cloud http://10.12.3.13/k3cloud
	public static String POST_K3CloudURL = "http://10.12.3.13/k3cloud";

	// Cookie 值
	public static String CookieVal = null;

	//重复登录次数
	private static int count = 0;


	private static Map<String, String> map = new HashMap<String, String>();
	static {
		map.put("Save",
				"Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc");
		map.put("BatchSave",
				"Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.BatchSave.common.kdsvc");
		map.put("View",
				"Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.View.common.kdsvc");
		map.put("Submit",
				"Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit.common.kdsvc");
		map.put("Audit",
				"Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Audit.common.kdsvc");
		map.put("UnAudit",
				"Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.UnAudit.common.kdsvc");
		map.put("Delete",
				"Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Delete.common.kdsvc");
		map.put("StatusConvert",
				"Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.StatusConvert.common.kdsvc");
		map.put("ExecuteBillQuery",
				"Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc");
		map.put("ExcuteQueryPOOrder",
				"Kingdee.K3Cloud.WebAPI.Service.DBHelper.ExcuteQueryPOOrder,Kingdee.K3Cloud.WebAPI.Service.common.kdsvc");
		map.put("ExcuteQuerySALOrder",
				"Kingdee.K3Cloud.WebAPI.Service.DBHelper.ExcuteQuerySALOrder,Kingdee.K3Cloud.WebAPI.Service.common.kdsvc");
		map.put("ExcuteQuerySALOut",
				"Kingdee.K3Cloud.WebAPI.Service.DBHelper.ExcuteQuerySALOut,Kingdee.K3Cloud.WebAPI.Service.common.kdsvc");
		map.put("ExcuteQuerySALReturn",
				"Kingdee.K3Cloud.WebAPI.Service.DBHelper.ExcuteQuerySALReturn,Kingdee.K3Cloud.WebAPI.Service.common.kdsvc");
		map.put("ExcuteQuerySALSic",
				"Kingdee.K3Cloud.WebAPI.Service.DBHelper.ExcuteQuerySALSic,Kingdee.K3Cloud.WebAPI.Service.common.kdsvc");
		map.put("ExcuteQueryDeleteRecord",
				"Kingdee.K3Cloud.WebAPI.Service.DBHelper.ExcuteQueryDeleteRecord,Kingdee.K3Cloud.WebAPI.Service.common.kdsvc");
		map.put("ExecuteQuerySql",
				"Kingdee.K3Cloud.WebAPI.Service.DBHelper.ExcuteQuerySql,Kingdee.K3Cloud.WebAPI.Service.common.kdsvc");
		// 金税开具接口
		map.put("InvoiceGTOperate",
				"Kingdee.K3Cloud.WebAPI.Service.InvoiceGoldenTax.InvoiceGTOperate,Kingdee.K3Cloud.WebAPI.Service.common.kdsvc");
		// 电子发票开具
		map.put("InvoiceEICreateIV",
				"Kingdee.K3Cloud.WebAPI.Service.InvoiceGoldenTax.InvoiceEICreateIV,Kingdee.K3Cloud.WebAPI.Service.common.kdsvc");
		// 金税作废接口
		map.put("InvoiceGTInvalidIV",
				"Kingdee.K3Cloud.WebAPI.Service.InvoiceGoldenTax.InvoiceGTInvalidIV,Kingdee.K3Cloud.WebAPI.Service.common.kdsvc");
		// 金税打印
		map.put("InvoiceGTPrint",
				"Kingdee.K3Cloud.WebAPI.Service.InvoiceGoldenTax.InvoiceGTPrint,Kingdee.K3Cloud.WebAPI.Service.common.kdsvc");
		// 金税清单打印
		map.put("InvoiceGTPrintList",
				"Kingdee.K3Cloud.WebAPI.Service.InvoiceGoldenTax.InvoiceGTPrintList,Kingdee.K3Cloud.WebAPI.Service.common.kdsvc");
	}






	// HttpURLConnection
	private static HttpURLConnection initUrlConn(String url, JSONArray paras)
			throws Exception {
		URL postUrl = new URL(POST_K3CloudURL.concat(url));
		HttpURLConnection connection = (HttpURLConnection) postUrl
				.openConnection();
		if (CookieVal != null) {
			connection.setRequestProperty("Cookie", CookieVal);
		}
		if (!connection.getDoOutput()) {
			connection.setDoOutput(true);
		}
		connection.setRequestMethod("POST");
		connection.setUseCaches(false);
		connection.setInstanceFollowRedirects(true);
		connection.setRequestProperty("Content-Type", "application/json");
		DataOutputStream out = new DataOutputStream(
				connection.getOutputStream());

		UUID uuid = UUID.randomUUID();
		int hashCode = uuid.toString().hashCode();

		JSONObject jObj = new JSONObject();

		jObj.put("format", 1);
		jObj.put("useragent", "ApiClient");
		jObj.put("rid", hashCode);
		jObj.put("parameters", chinaToUnicode(paras.toString()));
		jObj.put("timestamp", new Date().toString());
		jObj.put("v", "1.0");

		log.info("K3请求信息:{}", jObj.toString());

		out.writeBytes(jObj.toString());
		out.flush();
		out.close();

		return connection;
	}

	// Login
	public static boolean Login(String dbId, String user, String pwd, int lang)
			throws Exception {

		if(CookieVal != null){
			return true;
		}
		boolean bResult = false;
		//kdservice-sessionid=fbe9ce6b-f87e-492c-b25d-b3a80c02efb3; path=/
		String sUrl = "Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc";

		JSONArray jParas = new JSONArray();
		jParas.add(dbId);// 帐套Id
		jParas.add(user);// 用户名
		jParas.add(pwd);// 密码
		jParas.add(lang);// 语言

		HttpURLConnection connection = initUrlConn(sUrl, jParas);
		// 获取Cookie
		String key = null;
		log.info("K3登录获取的所有头信息:{}", JSON.toJSONString(connection.getHeaderFields()));
		for (int i = 1; (key = connection.getHeaderFieldKey(i)) != null; i++) {
			if (key.equalsIgnoreCase("Set-Cookie")) {
				String tempCookieVal = connection.getHeaderField(i);
				if (tempCookieVal.startsWith("kdservice-sessionid")) {
					log.info("K3登录获取的CookieVal:{}", tempCookieVal);
					CookieVal = tempCookieVal;
					break;
				}
			}
		}

		BufferedReader reader = new BufferedReader(new InputStreamReader(
				connection.getInputStream()));
		String line;
		log.info(" ============================= ");
		log.info(" Contents of post request ");
		log.info(" ============================= ");
		while ((line = reader.readLine()) != null) {
			String sResult = new String(line.getBytes(), "utf-8");
			log.info(sResult);
			bResult = line.contains("\"LoginResultType\":1");
		}
		log.info(" ============================= ");
		log.info(" Contents of post request ends ");
		log.info(" ============================= ");
		reader.close();

		connection.disconnect();

		return bResult;
	}

	/**
	 * 把中文转成Unicode码
	 *
	 * @param str
	 * @return
	 */
	public static String chinaToUnicode(String str) {
		StringBuilder result = new StringBuilder();
		for (int i = 0; i < str.length(); i++) {
			char chr1 = str.charAt(i);
			if(chr1 == 160 || chr1 == 12288) { // 不间断空格与全角空格转换为半角空格
				result.append(" ");
			}
//			else if(chr1 == 0xb0) { // 特殊符号度数符号处理   https://www.fuhaozi.com/unicode/0/00B0.html
//				result.append("\\u00b0");
//			}
//			else if(chr1 == 215 || chr1 >= 256) {// 汉字范围 \u4e00-\u9fa5 (中文)    字符'×'也需要转义
			else if(chr1 >= 161) {// 汉字范围 \u4e00-\u9fa5 (中文)    字符'×'也需要转义
				result.append("\\u");
				String hex = Integer.toHexString(chr1);
				if(hex.length() == 2) {
					result.append("00");
				} else if(hex.length() == 3) {
					result.append("0");
				}
				result.append(hex);
			}else {
				result.append(chr1);
			}
		}
		return result.toString();
	}

}
