package com.chinamobile.iot.sc.dto.response.k3;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @package: com.chinamobile.iot.sc.dto.response.k3
 * @ClassName: K3Result
 * @description: K3系统返回错误信息对象
 * @author: zyj
 * @create: 2022/2/16 8:58
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
public class K3Result {

    private Result Result;

    @Data
    public static class Result{
        private ResponseStatus ResponseStatus;
    }

    @Data
    public static class ResponseStatus{
        private Double ErrorCode;
        private Boolean IsSuccess;
        private List<ErrorMsg> Errors;
        private List<Object> SuccessEntitys;
        private List<Object> SuccessMessages;
        private Double MsgCode;
    }

    @Data
    public static class ErrorMsg{
        private String Message;
        private Double DIndex;
    }

    public static void main(String[] args) {
        String errorStr = "{\"Result\":{\"ResponseStatus\":{\"ErrorCode\":500.0,\"IsSuccess\":false,\"Errors\":[{\"Message\":\"会话信息已丢失，请重新登录\",\"DIndex\":0.0}],\"SuccessEntitys\":[],\"SuccessMessages\":[],\"MsgCode\":1.0}}}";

        try {


            K3Result k3Result = JSONObject.parseObject(errorStr, K3Result.class);
            List<Object> list = new ArrayList<>();
            list.add(k3Result);
            List<List<Object>> lists = new ArrayList<>();
            lists.add(list);
            System.out.println(k3Result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
