package com.chinamobile.iot.sc.dto.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @package: com.chinamobile.iot.sc.dto.response
 * @ClassName: EStoreOrderInfo
 * @description: 外部电商订单信息
 * @author: zyj
 * @create: 2022/2/15 14:14
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@Accessors(chain = true)
public class EStoreOrderInfo {
    /**
     * 下单时间
     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date orderDate;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 订单类型
     */
    private String type;
    /**
     * 商品名称
     */
    private String goodeName;
    /**
     * 单价
     */
    private Double unitPrice;
    /**
     * 订购数量
     */
    private Double qty;
    /**
     * 订单金额
     */
    private Double receiveAmt;
    /**
     * 来源网店
     */
    private String sourceName;
    /**
     * 收货省
     */
    private String receiveProvince;
    /**
     * 订单状态
     */
    private String status;
    /**
     * 线下处理状态
     */
    private String processStatus;

}
