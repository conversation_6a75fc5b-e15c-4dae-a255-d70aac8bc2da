package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.dto.request.EStore4PageRequest;
import com.chinamobile.iot.sc.dto.response.EStoreOrderInfo;
import com.chinamobile.iot.sc.mode.PageData;

/**
 * @package: com.chinamobile.iot.sc.service
 * @ClassName: IK3CloudApiService
 * @description: 调用K3系统Service接口
 * @author: zyj
 * @create: 2022/2/15 14:12
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
public interface IK3CloudApiService {

    BaseAnswer<PageData<EStoreOrderInfo>> page4K3OrderInfo(EStore4PageRequest request);

}
