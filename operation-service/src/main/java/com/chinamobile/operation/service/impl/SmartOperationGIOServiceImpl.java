package com.chinamobile.operation.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.operation.config.RestTemplateConfig;
import com.chinamobile.operation.dao.UserScreenMapper;
import com.chinamobile.operation.pojo.response.GioGroupDetailResponse;
import com.chinamobile.operation.pojo.response.GioGroupResponse;
import com.chinamobile.operation.pojo.response.GioGroupUserListResponse;
import com.chinamobile.operation.service.SmartOperationGIOService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SmartOperationGIOServiceImpl implements SmartOperationGIOService {
    @Resource
    private UserScreenMapper userScreenMapper;
    @Value("${gio.url}")
    private String gioUrl;

    @Value("${gio.projectId}")
    private String gioProjectId;

    @Value("${gio.spaceId}")
    private String gioSpaceId;

    @Value("${gio.token}")
    private String gioToken;

    @Override
    public BaseAnswer<List<GioGroupDetailResponse>> getGroupDetailList(String name) {
        BaseAnswer<List<GioGroupDetailResponse>> answer = new BaseAnswer<>();
        String requestUrl = gioUrl + "/v1/api/projects/" + gioProjectId +"/spaces/" + gioSpaceId + "/segment_profiles";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("content-type", "application/json;charset=utf-8");
            headers.add("Authorization", gioToken);
            log.info("请求gio群组request:{}",JSON.toJSONString(requestUrl));
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            ResponseEntity<String> response = restTemplateHttps.exchange(requestUrl, HttpMethod.GET, new HttpEntity<>(headers), String.class);
            log.info("请求gio群组response:{}",JSON.toJSONString(response));
            GioGroupResponse response1 = JSON.parseObject(response.getBody(), GioGroupResponse.class);
            if(StringUtils.isNotEmpty(name)){
                answer.setData(response1.getValues().stream().filter(item->item.getName().contains(name)).collect(Collectors.toList()));
            }else{
                answer.setData(response1.getValues());
            }
        }catch (Exception e) {
            log.error("getGroupDetailList发生异常",e);
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,e.getMessage());
        }
        return answer;
    }


    public List<GioGroupDetailResponse> getGioGroupNameList(List<String> groupId) throws Exception {
        if(groupId == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"缺少groupId");
        }
        String requestUrl = gioUrl + "/v1/api/projects/" + gioProjectId +"/spaces/" + gioSpaceId + "/segment_profiles";
        HttpHeaders headers = new HttpHeaders();
        headers.add("content-type", "application/json;charset=utf-8");
        headers.add("Authorization", gioToken);
        log.info("请求gio群组request:{}",JSON.toJSONString(requestUrl));
        RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
        ResponseEntity<String> response = restTemplateHttps.exchange(requestUrl, HttpMethod.GET, new HttpEntity<>(headers), String.class);
        log.info("请求gio群组用户列表response:{}",JSON.toJSONString(response));
        GioGroupResponse response1 = JSON.parseObject(response.getBody(), GioGroupResponse.class);
        List<GioGroupDetailResponse> resultList = response1.getValues().stream().filter(
                item->groupId.contains(item.getKey())
        ).collect(Collectors.toList());
        return resultList;
    }

    /**
     * 根据群组id获取用户电话号码列表
     * @param groupId
     * @return
     */
    public List<String> getGioUserInfo (String groupId) throws Exception{
        if(groupId == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"缺少groupId");
        }
        String requestUrl = gioUrl + "/v1/api/projects/" + gioProjectId +"/spaces/" + gioSpaceId + "/segment_profiles/" + groupId +"/users?properties[]=usr_wlw_custID" ;
        HttpHeaders headers = new HttpHeaders();
        headers.add("content-type", "application/json;charset=utf-8");
        headers.add("Authorization", gioToken);
        log.info("请求gio群组获取手机号request:{}",JSON.toJSONString(requestUrl));
        RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
        ResponseEntity<String> response = restTemplateHttps.exchange(requestUrl, HttpMethod.GET, new HttpEntity<>(headers), String.class);
        log.info("请求gio群组获取手机号response:{}",JSON.toJSONString(response));
        GioGroupUserListResponse response1 = JSON.parseObject(response.getBody(), GioGroupUserListResponse.class);

        List<String> phoneList = response1.getValues().stream().map(item->item.getProperties().getUsr_wlw_custID()).collect(Collectors.toList());

//        List<UserScreen> userScreenList = userScreenMapper.selectByExample(
//                new UserScreenExample().createCriteria()
//                        .andIdIn(userIdList)
//                        .example()
//        );
//        List<String> result = userScreenList.stream().map(item->item.getPhone()).collect(Collectors.toList());
        log.info("请求gio群组获取手机号结果 :{}",JSON.toJSONString(phoneList));
        return phoneList;

    }


}
