package com.chinamobile.operation.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.operation.pojo.param.GetBlacklistListParam;
import com.chinamobile.operation.pojo.vo.GetBlacklistListVO;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

public interface SmartOperationBlacklistService {

    BaseAnswer<PageData<GetBlacklistListVO>> getBlacklistList(GetBlacklistListParam param);

    BaseAnswer<Void> deleteBlacklist(String id);
    @Transactional(rollbackFor = Exception.class)
    BaseAnswer<Void> importBlacklist(MultipartFile file,
                                         LoginIfo4Redis loginIfo4Redis,
                                         HttpServletResponse response);
}
