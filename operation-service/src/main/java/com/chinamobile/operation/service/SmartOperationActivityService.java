package com.chinamobile.operation.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.operation.pojo.param.ActivityListParam;
import com.chinamobile.operation.pojo.param.AuditActivityParam;
import com.chinamobile.operation.pojo.param.SaveActivityParam;
import com.chinamobile.operation.pojo.vo.ActivityDetailVO;
import com.chinamobile.operation.pojo.vo.ActivityListVO;

public interface SmartOperationActivityService {
    BaseAnswer saveActivity(SaveActivityParam param, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<PageData<ActivityListVO>> getActivityList(ActivityListParam param,Boolean withGroupInfo);

    BaseAnswer pauseActivity(String id);

    BaseAnswer resumeActivity(String id);

    BaseAnswer closeActivity(String id);

    BaseAnswer deleteActivity(String id);

    BaseAnswer releaseActivity(String id);

    BaseAnswer<ActivityDetailVO> getActivityDetail(String id);

    BaseAnswer auditActivity(AuditActivityParam param, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer hasPermission(LoginIfo4Redis loginIfo4Redis);
}
