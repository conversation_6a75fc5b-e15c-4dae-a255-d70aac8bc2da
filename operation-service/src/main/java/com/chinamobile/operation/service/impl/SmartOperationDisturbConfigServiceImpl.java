package com.chinamobile.operation.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.operation.dao.SmartOperationDisturbConfigMapper;
import com.chinamobile.operation.pojo.entity.SmartOperationDisturbConfig;
import com.chinamobile.operation.pojo.entity.SmartOperationDisturbConfigExample;
import com.chinamobile.operation.pojo.param.SaveDisturbConfigParam;
import com.chinamobile.operation.pojo.vo.GetDisturbConfigListVO;
import com.chinamobile.operation.service.SmartOperationDisturbConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class SmartOperationDisturbConfigServiceImpl implements SmartOperationDisturbConfigService {



    @Resource
    private SmartOperationDisturbConfigMapper smartOperationDisturbConfigMapper;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private RedisUtil redisUtil;

    private static final String disturb_key = "disturb_limit";

    private static final String disturb_lock_key = "disturb_lock";


    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseAnswer<Void> saveDisturbConfig(SaveDisturbConfigParam param) {
        Date now = new Date();
        String id = param.getId();
        if(param.getDisturbSwitch()){
            if(param.getDisturbStartTime() == null || param.getDisturbEndTime() == null){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "防扰开关打开时必须设置时间");
            }
        }
        if(param.getFrequencySwitch()){
            if(param.getFrequencyCountLimit() == null || param.getFrequencyCountLimit() == null){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "频次打开时必须设置频次");
            }
        }
        if(StringUtils.isNotEmpty(id)) {
            SmartOperationDisturbConfig config = smartOperationDisturbConfigMapper.selectByPrimaryKey(id);
            if (config == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "对应配置不存在");
            }
            BeanUtils.copyProperties(param,config);
            config.setUpdateTime(now);
            smartOperationDisturbConfigMapper.updateByPrimaryKey(config);

            //清空redis
            deleteKeysByPrefixWithScan(disturb_key + "_" + config.getChannel());
        }else{
            List<SmartOperationDisturbConfig> configList = smartOperationDisturbConfigMapper.selectByExample(
                    new SmartOperationDisturbConfigExample().createCriteria()
                            .andChannelEqualTo(param.getChannel())
                            .example()
            );
            if(configList.size()>0){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "同一渠道只能存在一个对应配置");
            }
            SmartOperationDisturbConfig config = new SmartOperationDisturbConfig();
            BeanUtils.copyProperties(param,config);
            config.setCreateTime(now);
            config.setUpdateTime(now);
            config.setId(BaseServiceUtils.getId());
            smartOperationDisturbConfigMapper.insert(config);
        }



        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<List<GetDisturbConfigListVO>> getDisturbConfigList() {
        List<SmartOperationDisturbConfig> disturbConfigList = smartOperationDisturbConfigMapper.selectByExample(
                new SmartOperationDisturbConfigExample().createCriteria().example()
        );
        return BaseAnswer.success(disturbConfigList);
    }

    public Boolean getPhoneEnable (String phone, Integer channel){
        return redisUtil.smartLock(disturb_lock_key + "_" + channel + "_" + phone, () -> {
            Integer count = (Integer) redisTemplate.opsForValue().get(disturb_key + "_" + channel + "_" + phone);
            List<SmartOperationDisturbConfig> configList = smartOperationDisturbConfigMapper.selectByExample(
                    new SmartOperationDisturbConfigExample().createCriteria()
                            .andChannelEqualTo(channel.toString())
                            .example()
            );
            if (CollectionUtils.isEmpty(configList)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "对应配置不存在");
            }
            SmartOperationDisturbConfig config = configList.get(0);
            // 创建时间格式化器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");

            if (StringUtils.isNotEmpty(config.getDisturbStartTime()) && StringUtils.isNotEmpty(config.getDisturbEndTime())) {
                // 解析时间字符串
                LocalTime startTime = LocalTime.parse(config.getDisturbStartTime(), formatter);
                LocalTime endTime = LocalTime.parse(config.getDisturbEndTime(), formatter);

                // 获取当前时间
                LocalTime currentTime = LocalTime.now();

                if (isTimeInInterval(currentTime, startTime, endTime)) {
                    return false;
                }
            }
            if (null == count) {
                return true;
            }else{
                if(config.getFrequencyCountLimit() == null){
                    return true;
                }
                if(count < config.getFrequencyCountLimit()){
                    return true;
                }else{
                    return false;
                }
            }

        });

    }

    public Boolean increasePhoneCount(String phone, Integer channel){
        return redisUtil.smartLock(disturb_lock_key + "_" + channel + "_" + phone, () -> {
            String redisKey = disturb_key + "_" + channel + "_" + phone;
            Integer count = (Integer) redisTemplate.opsForValue().get(redisKey);
            Boolean exists = false;
            if (null == count) {
                count = 1;
            }else{
                count++;
                exists = true;
            }
            List<SmartOperationDisturbConfig> configList = smartOperationDisturbConfigMapper.selectByExample(
                    new SmartOperationDisturbConfigExample().createCriteria()
                            .andChannelEqualTo(channel.toString())
                            .example()
            );
            if (CollectionUtils.isEmpty(configList)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "对应配置不存在");
            }
            SmartOperationDisturbConfig config = configList.get(0);
            if(config.getFrequencyDayLimit() == null){
                return true;
            }
            if (!exists) {
                // 首次设置：添加过期时间
                redisTemplate.opsForValue().set(redisKey, count, config.getFrequencyDayLimit(), TimeUnit.DAYS);
            } else {
                // 后续更新：仅修改值
                redisTemplate.opsForValue().set(redisKey, count);
            }
            return true;
        });
    }

    public void deleteKeysByPrefixWithScan(String prefix) {
        String pattern = prefix + "*";

        // 获取 Redis 连接
        RedisConnection connection = redisTemplate.getConnectionFactory().getConnection();

        // 保存匹配到的key的集合
        Set<byte[]> rawKeys = new HashSet<>();

        // 游标初始值从0开始
        Cursor<byte[]> cursor = null;
        try {
            cursor = connection.scan(ScanOptions.scanOptions()
                    .match(pattern)
                    .count(100) // 每次扫描批量大小（按需调整）
                    .build());

            while (cursor.hasNext()) {
                rawKeys.add(cursor.next());
            }

            // 将二进制数据转为实际使用的key格式
            Set<String> keys = new HashSet<>();
            for (byte[] rawKey : rawKeys) {
                keys.add(new String(rawKey, "UTF-8")); // 或使用redisTemplate的序列化器转换
            }

            // 批量删除key
            if (!keys.isEmpty()) {
                redisTemplate.delete(keys);
            }
        } catch (Exception e) {
            throw new RuntimeException("Redis SCAN删除失败", e);
        } finally {
            if (cursor != null) {
                try {
                    cursor.close(); // 务必将游标关闭！
                } catch (Exception ignored) {}
            }
            connection.close(); // 归还连接到连接池
        }
    }
    public static boolean isTimeInInterval(LocalTime current, LocalTime start, LocalTime end) {
        if (start.isBefore(end)) {
            // 不跨天：当前时间在 [start, end] 区间内
            return !current.isBefore(start) && !current.isAfter(end);
        } else {
            // 跨天：当前时间在 [start, 23:59:59.999...] 或 [00:00:00, end] 区间内
            return !current.isBefore(start) || !current.isAfter(end);
        }
    }
}
