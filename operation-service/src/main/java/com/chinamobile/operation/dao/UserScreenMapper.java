package com.chinamobile.operation.dao;

import com.chinamobile.operation.pojo.entity.UserScreen;
import com.chinamobile.operation.pojo.entity.UserScreenExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserScreenMapper {
    /**
     *
     * @mbg.generated Wed Feb 26 16:18:22 CST 2025
     */
    long countByExample(UserScreenExample example);

    /**
     *
     * @mbg.generated Wed Feb 26 16:18:22 CST 2025
     */
    int deleteByExample(UserScreenExample example);

    /**
     *
     * @mbg.generated Wed Feb 26 16:18:22 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Feb 26 16:18:22 CST 2025
     */
    int insert(UserScreen record);

    /**
     *
     * @mbg.generated Wed Feb 26 16:18:22 CST 2025
     */
    int insertSelective(UserScreen record);

    /**
     *
     * @mbg.generated Wed Feb 26 16:18:22 CST 2025
     */
    List<UserScreen> selectByExample(UserScreenExample example);

    /**
     *
     * @mbg.generated Wed Feb 26 16:18:22 CST 2025
     */
    UserScreen selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Feb 26 16:18:22 CST 2025
     */
    int updateByExampleSelective(@Param("record") UserScreen record, @Param("example") UserScreenExample example);

    /**
     *
     * @mbg.generated Wed Feb 26 16:18:22 CST 2025
     */
    int updateByExample(@Param("record") UserScreen record, @Param("example") UserScreenExample example);

    /**
     *
     * @mbg.generated Wed Feb 26 16:18:22 CST 2025
     */
    int updateByPrimaryKeySelective(UserScreen record);

    /**
     *
     * @mbg.generated Wed Feb 26 16:18:22 CST 2025
     */
    int updateByPrimaryKey(UserScreen record);

    /**
     *
     * @mbg.generated Wed Feb 26 16:18:22 CST 2025
     */
    int batchInsert(@Param("list") List<UserScreen> list);

    /**
     *
     * @mbg.generated Wed Feb 26 16:18:22 CST 2025
     */
    int batchInsertSelective(@Param("list") List<UserScreen> list, @Param("selective") UserScreen.Column ... selective);
}