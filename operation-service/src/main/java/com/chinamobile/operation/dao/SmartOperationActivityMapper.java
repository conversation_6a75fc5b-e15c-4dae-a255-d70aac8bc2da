package com.chinamobile.operation.dao;

import com.chinamobile.operation.pojo.entity.SmartOperationActivity;
import com.chinamobile.operation.pojo.entity.SmartOperationActivityExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SmartOperationActivityMapper {
    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    long countByExample(SmartOperationActivityExample example);

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    int deleteByExample(SmartOperationActivityExample example);

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    int insert(SmartOperationActivity record);

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    int insertSelective(SmartOperationActivity record);

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    List<SmartOperationActivity> selectByExample(SmartOperationActivityExample example);

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    SmartOperationActivity selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    int updateByExampleSelective(@Param("record") SmartOperationActivity record, @Param("example") SmartOperationActivityExample example);

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    int updateByExample(@Param("record") SmartOperationActivity record, @Param("example") SmartOperationActivityExample example);

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    int updateByPrimaryKeySelective(SmartOperationActivity record);

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    int updateByPrimaryKey(SmartOperationActivity record);

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    int batchInsert(@Param("list") List<SmartOperationActivity> list);

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    int batchInsertSelective(@Param("list") List<SmartOperationActivity> list, @Param("selective") SmartOperationActivity.Column ... selective);
}