package com.chinamobile.operation.dao;

import com.chinamobile.operation.pojo.entity.SmartOperationActivityGroup;
import com.chinamobile.operation.pojo.entity.SmartOperationActivityGroupExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SmartOperationActivityGroupMapper {
    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    long countByExample(SmartOperationActivityGroupExample example);

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    int deleteByExample(SmartOperationActivityGroupExample example);

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    int insert(SmartOperationActivityGroup record);

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    int insertSelective(SmartOperationActivityGroup record);

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    List<SmartOperationActivityGroup> selectByExample(SmartOperationActivityGroupExample example);

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    SmartOperationActivityGroup selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    int updateByExampleSelective(@Param("record") SmartOperationActivityGroup record, @Param("example") SmartOperationActivityGroupExample example);

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    int updateByExample(@Param("record") SmartOperationActivityGroup record, @Param("example") SmartOperationActivityGroupExample example);

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    int updateByPrimaryKeySelective(SmartOperationActivityGroup record);

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    int updateByPrimaryKey(SmartOperationActivityGroup record);

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    int batchInsert(@Param("list") List<SmartOperationActivityGroup> list);

    /**
     *
     * @mbg.generated Mon Feb 24 15:24:34 CST 2025
     */
    int batchInsertSelective(@Param("list") List<SmartOperationActivityGroup> list, @Param("selective") SmartOperationActivityGroup.Column ... selective);
}