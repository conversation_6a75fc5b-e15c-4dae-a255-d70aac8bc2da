package com.chinamobile.operation.dao.ext;

import com.chinamobile.operation.pojo.mapper.GetSmsTemplateListDO;
import com.chinamobile.operation.pojo.param.GetSmsTemplateListParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SmartOperationSmsTemplateMapperExt {

   List<GetSmsTemplateListDO> getSmsTemplateList(@Param(value = "param") GetSmsTemplateListParam param);
}