package com.chinamobile.operation.dao.ext;

import com.chinamobile.operation.pojo.mapper.GetBlacklistListDO;
import com.chinamobile.operation.pojo.param.GetBlacklistListParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SmartOperationBlacklistMapperExt {
   List<GetBlacklistListDO> getBlacklistList(@Param(value = "param") GetBlacklistListParam param);
}