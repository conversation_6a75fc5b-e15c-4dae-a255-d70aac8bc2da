package com.chinamobile.operation.dao;

import com.chinamobile.operation.pojo.entity.SmartOperationDisturbConfig;
import com.chinamobile.operation.pojo.entity.SmartOperationDisturbConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SmartOperationDisturbConfigMapper {
    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    long countByExample(SmartOperationDisturbConfigExample example);

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    int deleteByExample(SmartOperationDisturbConfigExample example);

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    int insert(SmartOperationDisturbConfig record);

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    int insertSelective(SmartOperationDisturbConfig record);

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    List<SmartOperationDisturbConfig> selectByExample(SmartOperationDisturbConfigExample example);

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    SmartOperationDisturbConfig selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    int updateByExampleSelective(@Param("record") SmartOperationDisturbConfig record, @Param("example") SmartOperationDisturbConfigExample example);

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    int updateByExample(@Param("record") SmartOperationDisturbConfig record, @Param("example") SmartOperationDisturbConfigExample example);

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    int updateByPrimaryKeySelective(SmartOperationDisturbConfig record);

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    int updateByPrimaryKey(SmartOperationDisturbConfig record);

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    int batchInsert(@Param("list") List<SmartOperationDisturbConfig> list);

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    int batchInsertSelective(@Param("list") List<SmartOperationDisturbConfig> list, @Param("selective") SmartOperationDisturbConfig.Column ... selective);
}