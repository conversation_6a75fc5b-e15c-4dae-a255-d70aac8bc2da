package com.chinamobile.operation.dao;

import com.chinamobile.operation.pojo.entity.SmartOperationBlacklist;
import com.chinamobile.operation.pojo.entity.SmartOperationBlacklistExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SmartOperationBlacklistMapper {
    /**
     *
     * @mbg.generated Tue Feb 25 14:34:46 CST 2025
     */
    long countByExample(SmartOperationBlacklistExample example);

    /**
     *
     * @mbg.generated Tue Feb 25 14:34:46 CST 2025
     */
    int deleteByExample(SmartOperationBlacklistExample example);

    /**
     *
     * @mbg.generated Tue Feb 25 14:34:46 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Feb 25 14:34:46 CST 2025
     */
    int insert(SmartOperationBlacklist record);

    /**
     *
     * @mbg.generated Tue Feb 25 14:34:46 CST 2025
     */
    int insertSelective(SmartOperationBlacklist record);

    /**
     *
     * @mbg.generated Tue Feb 25 14:34:46 CST 2025
     */
    List<SmartOperationBlacklist> selectByExample(SmartOperationBlacklistExample example);

    /**
     *
     * @mbg.generated Tue Feb 25 14:34:46 CST 2025
     */
    SmartOperationBlacklist selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Feb 25 14:34:46 CST 2025
     */
    int updateByExampleSelective(@Param("record") SmartOperationBlacklist record, @Param("example") SmartOperationBlacklistExample example);

    /**
     *
     * @mbg.generated Tue Feb 25 14:34:46 CST 2025
     */
    int updateByExample(@Param("record") SmartOperationBlacklist record, @Param("example") SmartOperationBlacklistExample example);

    /**
     *
     * @mbg.generated Tue Feb 25 14:34:46 CST 2025
     */
    int updateByPrimaryKeySelective(SmartOperationBlacklist record);

    /**
     *
     * @mbg.generated Tue Feb 25 14:34:46 CST 2025
     */
    int updateByPrimaryKey(SmartOperationBlacklist record);

    /**
     *
     * @mbg.generated Tue Feb 25 14:34:46 CST 2025
     */
    int batchInsert(@Param("list") List<SmartOperationBlacklist> list);

    /**
     *
     * @mbg.generated Tue Feb 25 14:34:46 CST 2025
     */
    int batchInsertSelective(@Param("list") List<SmartOperationBlacklist> list, @Param("selective") SmartOperationBlacklist.Column ... selective);
}