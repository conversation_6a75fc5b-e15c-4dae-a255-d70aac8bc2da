package com.chinamobile.operation.enums;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2025/2/24 10:21
 */
public enum SmartOperationActivityConditionEnum {

    ONECE(1,"单次触发"),
    PERIOD(2,"周期触发"),
    ;

    public Integer code;
    public String name;

    SmartOperationActivityConditionEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Boolean contains(Integer code){
        SmartOperationActivityConditionEnum[] values = SmartOperationActivityConditionEnum.values();
        for (SmartOperationActivityConditionEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return true;
            }
        }
        return false;
    }

}
