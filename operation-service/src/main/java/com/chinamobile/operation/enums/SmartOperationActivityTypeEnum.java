package com.chinamobile.operation.enums;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2025/2/24 10:21
 */
public enum SmartOperationActivityTypeEnum {

    SMS(1,"短信"),
    OFFICIAL_ACCOUNT(2,"公众号"),
    MINI_PROGRAM(3,"小程序"),
    ;

    public Integer code;
    public String name;

    SmartOperationActivityTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Boolean contains(Integer code){
        SmartOperationActivityTypeEnum[] values = SmartOperationActivityTypeEnum.values();
        for (SmartOperationActivityTypeEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return true;
            }
        }
        return false;
    }

}
