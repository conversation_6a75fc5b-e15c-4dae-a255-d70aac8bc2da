package com.chinamobile.operation.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SmartOperationDisturbConfigExample {
    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public SmartOperationDisturbConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public SmartOperationDisturbConfigExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public SmartOperationDisturbConfigExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        SmartOperationDisturbConfigExample example = new SmartOperationDisturbConfigExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public SmartOperationDisturbConfigExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public SmartOperationDisturbConfigExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andChannelIsNull() {
            addCriterion("channel is null");
            return (Criteria) this;
        }

        public Criteria andChannelIsNotNull() {
            addCriterion("channel is not null");
            return (Criteria) this;
        }

        public Criteria andChannelEqualTo(String value) {
            addCriterion("channel =", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("channel = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelNotEqualTo(String value) {
            addCriterion("channel <>", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("channel <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThan(String value) {
            addCriterion("channel >", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("channel > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThanOrEqualTo(String value) {
            addCriterion("channel >=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("channel >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelLessThan(String value) {
            addCriterion("channel <", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("channel < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelLessThanOrEqualTo(String value) {
            addCriterion("channel <=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("channel <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelLike(String value) {
            addCriterion("channel like", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotLike(String value) {
            addCriterion("channel not like", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelIn(List<String> values) {
            addCriterion("channel in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotIn(List<String> values) {
            addCriterion("channel not in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelBetween(String value1, String value2) {
            addCriterion("channel between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotBetween(String value1, String value2) {
            addCriterion("channel not between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchIsNull() {
            addCriterion("frequency_switch is null");
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchIsNotNull() {
            addCriterion("frequency_switch is not null");
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchEqualTo(Boolean value) {
            addCriterion("frequency_switch =", value, "frequencySwitch");
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_switch = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchNotEqualTo(Boolean value) {
            addCriterion("frequency_switch <>", value, "frequencySwitch");
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchNotEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_switch <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchGreaterThan(Boolean value) {
            addCriterion("frequency_switch >", value, "frequencySwitch");
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchGreaterThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_switch > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchGreaterThanOrEqualTo(Boolean value) {
            addCriterion("frequency_switch >=", value, "frequencySwitch");
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchGreaterThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_switch >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchLessThan(Boolean value) {
            addCriterion("frequency_switch <", value, "frequencySwitch");
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchLessThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_switch < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchLessThanOrEqualTo(Boolean value) {
            addCriterion("frequency_switch <=", value, "frequencySwitch");
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchLessThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_switch <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchIn(List<Boolean> values) {
            addCriterion("frequency_switch in", values, "frequencySwitch");
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchNotIn(List<Boolean> values) {
            addCriterion("frequency_switch not in", values, "frequencySwitch");
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchBetween(Boolean value1, Boolean value2) {
            addCriterion("frequency_switch between", value1, value2, "frequencySwitch");
            return (Criteria) this;
        }

        public Criteria andFrequencySwitchNotBetween(Boolean value1, Boolean value2) {
            addCriterion("frequency_switch not between", value1, value2, "frequencySwitch");
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitIsNull() {
            addCriterion("frequency_day_limit is null");
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitIsNotNull() {
            addCriterion("frequency_day_limit is not null");
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitEqualTo(Integer value) {
            addCriterion("frequency_day_limit =", value, "frequencyDayLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_day_limit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitNotEqualTo(Integer value) {
            addCriterion("frequency_day_limit <>", value, "frequencyDayLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitNotEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_day_limit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitGreaterThan(Integer value) {
            addCriterion("frequency_day_limit >", value, "frequencyDayLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitGreaterThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_day_limit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("frequency_day_limit >=", value, "frequencyDayLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitGreaterThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_day_limit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitLessThan(Integer value) {
            addCriterion("frequency_day_limit <", value, "frequencyDayLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitLessThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_day_limit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitLessThanOrEqualTo(Integer value) {
            addCriterion("frequency_day_limit <=", value, "frequencyDayLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitLessThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_day_limit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitIn(List<Integer> values) {
            addCriterion("frequency_day_limit in", values, "frequencyDayLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitNotIn(List<Integer> values) {
            addCriterion("frequency_day_limit not in", values, "frequencyDayLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitBetween(Integer value1, Integer value2) {
            addCriterion("frequency_day_limit between", value1, value2, "frequencyDayLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyDayLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("frequency_day_limit not between", value1, value2, "frequencyDayLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitIsNull() {
            addCriterion("frequency_count_limit is null");
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitIsNotNull() {
            addCriterion("frequency_count_limit is not null");
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitEqualTo(Integer value) {
            addCriterion("frequency_count_limit =", value, "frequencyCountLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_count_limit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitNotEqualTo(Integer value) {
            addCriterion("frequency_count_limit <>", value, "frequencyCountLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitNotEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_count_limit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitGreaterThan(Integer value) {
            addCriterion("frequency_count_limit >", value, "frequencyCountLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitGreaterThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_count_limit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("frequency_count_limit >=", value, "frequencyCountLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitGreaterThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_count_limit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitLessThan(Integer value) {
            addCriterion("frequency_count_limit <", value, "frequencyCountLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitLessThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_count_limit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitLessThanOrEqualTo(Integer value) {
            addCriterion("frequency_count_limit <=", value, "frequencyCountLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitLessThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("frequency_count_limit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitIn(List<Integer> values) {
            addCriterion("frequency_count_limit in", values, "frequencyCountLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitNotIn(List<Integer> values) {
            addCriterion("frequency_count_limit not in", values, "frequencyCountLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitBetween(Integer value1, Integer value2) {
            addCriterion("frequency_count_limit between", value1, value2, "frequencyCountLimit");
            return (Criteria) this;
        }

        public Criteria andFrequencyCountLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("frequency_count_limit not between", value1, value2, "frequencyCountLimit");
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchIsNull() {
            addCriterion("disturb_switch is null");
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchIsNotNull() {
            addCriterion("disturb_switch is not null");
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchEqualTo(Boolean value) {
            addCriterion("disturb_switch =", value, "disturbSwitch");
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_switch = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchNotEqualTo(Boolean value) {
            addCriterion("disturb_switch <>", value, "disturbSwitch");
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchNotEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_switch <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchGreaterThan(Boolean value) {
            addCriterion("disturb_switch >", value, "disturbSwitch");
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchGreaterThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_switch > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchGreaterThanOrEqualTo(Boolean value) {
            addCriterion("disturb_switch >=", value, "disturbSwitch");
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchGreaterThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_switch >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchLessThan(Boolean value) {
            addCriterion("disturb_switch <", value, "disturbSwitch");
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchLessThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_switch < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchLessThanOrEqualTo(Boolean value) {
            addCriterion("disturb_switch <=", value, "disturbSwitch");
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchLessThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_switch <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchIn(List<Boolean> values) {
            addCriterion("disturb_switch in", values, "disturbSwitch");
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchNotIn(List<Boolean> values) {
            addCriterion("disturb_switch not in", values, "disturbSwitch");
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchBetween(Boolean value1, Boolean value2) {
            addCriterion("disturb_switch between", value1, value2, "disturbSwitch");
            return (Criteria) this;
        }

        public Criteria andDisturbSwitchNotBetween(Boolean value1, Boolean value2) {
            addCriterion("disturb_switch not between", value1, value2, "disturbSwitch");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeIsNull() {
            addCriterion("disturb_start_time is null");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeIsNotNull() {
            addCriterion("disturb_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeEqualTo(String value) {
            addCriterion("disturb_start_time =", value, "disturbStartTime");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_start_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeNotEqualTo(String value) {
            addCriterion("disturb_start_time <>", value, "disturbStartTime");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeNotEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_start_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeGreaterThan(String value) {
            addCriterion("disturb_start_time >", value, "disturbStartTime");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeGreaterThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_start_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeGreaterThanOrEqualTo(String value) {
            addCriterion("disturb_start_time >=", value, "disturbStartTime");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeGreaterThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_start_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeLessThan(String value) {
            addCriterion("disturb_start_time <", value, "disturbStartTime");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeLessThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_start_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeLessThanOrEqualTo(String value) {
            addCriterion("disturb_start_time <=", value, "disturbStartTime");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeLessThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_start_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeLike(String value) {
            addCriterion("disturb_start_time like", value, "disturbStartTime");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeNotLike(String value) {
            addCriterion("disturb_start_time not like", value, "disturbStartTime");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeIn(List<String> values) {
            addCriterion("disturb_start_time in", values, "disturbStartTime");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeNotIn(List<String> values) {
            addCriterion("disturb_start_time not in", values, "disturbStartTime");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeBetween(String value1, String value2) {
            addCriterion("disturb_start_time between", value1, value2, "disturbStartTime");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeNotBetween(String value1, String value2) {
            addCriterion("disturb_start_time not between", value1, value2, "disturbStartTime");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeIsNull() {
            addCriterion("disturb_end_time is null");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeIsNotNull() {
            addCriterion("disturb_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeEqualTo(String value) {
            addCriterion("disturb_end_time =", value, "disturbEndTime");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_end_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeNotEqualTo(String value) {
            addCriterion("disturb_end_time <>", value, "disturbEndTime");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeNotEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_end_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeGreaterThan(String value) {
            addCriterion("disturb_end_time >", value, "disturbEndTime");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeGreaterThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_end_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeGreaterThanOrEqualTo(String value) {
            addCriterion("disturb_end_time >=", value, "disturbEndTime");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeGreaterThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_end_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeLessThan(String value) {
            addCriterion("disturb_end_time <", value, "disturbEndTime");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeLessThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_end_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeLessThanOrEqualTo(String value) {
            addCriterion("disturb_end_time <=", value, "disturbEndTime");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeLessThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("disturb_end_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeLike(String value) {
            addCriterion("disturb_end_time like", value, "disturbEndTime");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeNotLike(String value) {
            addCriterion("disturb_end_time not like", value, "disturbEndTime");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeIn(List<String> values) {
            addCriterion("disturb_end_time in", values, "disturbEndTime");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeNotIn(List<String> values) {
            addCriterion("disturb_end_time not in", values, "disturbEndTime");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeBetween(String value1, String value2) {
            addCriterion("disturb_end_time between", value1, value2, "disturbEndTime");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeNotBetween(String value1, String value2) {
            addCriterion("disturb_end_time not between", value1, value2, "disturbEndTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(SmartOperationDisturbConfig.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andChannelLikeInsensitive(String value) {
            addCriterion("upper(channel) like", value.toUpperCase(), "channel");
            return (Criteria) this;
        }

        public Criteria andDisturbStartTimeLikeInsensitive(String value) {
            addCriterion("upper(disturb_start_time) like", value.toUpperCase(), "disturbStartTime");
            return (Criteria) this;
        }

        public Criteria andDisturbEndTimeLikeInsensitive(String value) {
            addCriterion("upper(disturb_end_time) like", value.toUpperCase(), "disturbEndTime");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Tue Mar 04 09:41:12 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Tue Mar 04 09:41:12 CST 2025
         */
        private SmartOperationDisturbConfigExample example;

        /**
         *
         * @mbg.generated Tue Mar 04 09:41:12 CST 2025
         */
        protected Criteria(SmartOperationDisturbConfigExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Mar 04 09:41:12 CST 2025
         */
        public SmartOperationDisturbConfigExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Mar 04 09:41:12 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Mar 04 09:41:12 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Mar 04 09:41:12 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Mar 04 09:41:12 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Tue Mar 04 09:41:12 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Mar 04 09:41:12 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Mar 04 09:41:12 CST 2025
         */
        void example(com.chinamobile.operation.pojo.entity.SmartOperationDisturbConfigExample example);
    }
}