package com.chinamobile.operation.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 
 *
 * <AUTHOR>
public class SmartOperationSmsTemplate implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    private String id;

    /**
     * 模版名称
     *
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    private String templateName;

    /**
     * 模版id
     *
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    private String templateId;

    /**
     * 1-验证码，2-短信通知，3-推广短信
     *
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    private String templateType;

    /**
     * 模版内容
     *
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    private String templateContent;

    /**
     * 说明
     *
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    private String description;

    /**
     * 用户id
     *
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    private String userId;

    /**
     *
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..smart_operation_sms_template.id
     *
     * @return the value of supply_chain..smart_operation_sms_template.id
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public SmartOperationSmsTemplate withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_sms_template.id
     *
     * @param id the value for supply_chain..smart_operation_sms_template.id
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_sms_template.template_name
     *
     * @return the value of supply_chain..smart_operation_sms_template.template_name
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public String getTemplateName() {
        return templateName;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public SmartOperationSmsTemplate withTemplateName(String templateName) {
        this.setTemplateName(templateName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_sms_template.template_name
     *
     * @param templateName the value for supply_chain..smart_operation_sms_template.template_name
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_sms_template.template_id
     *
     * @return the value of supply_chain..smart_operation_sms_template.template_id
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public String getTemplateId() {
        return templateId;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public SmartOperationSmsTemplate withTemplateId(String templateId) {
        this.setTemplateId(templateId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_sms_template.template_id
     *
     * @param templateId the value for supply_chain..smart_operation_sms_template.template_id
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_sms_template.template_type
     *
     * @return the value of supply_chain..smart_operation_sms_template.template_type
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public String getTemplateType() {
        return templateType;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public SmartOperationSmsTemplate withTemplateType(String templateType) {
        this.setTemplateType(templateType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_sms_template.template_type
     *
     * @param templateType the value for supply_chain..smart_operation_sms_template.template_type
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_sms_template.template_content
     *
     * @return the value of supply_chain..smart_operation_sms_template.template_content
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public String getTemplateContent() {
        return templateContent;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public SmartOperationSmsTemplate withTemplateContent(String templateContent) {
        this.setTemplateContent(templateContent);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_sms_template.template_content
     *
     * @param templateContent the value for supply_chain..smart_operation_sms_template.template_content
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public void setTemplateContent(String templateContent) {
        this.templateContent = templateContent;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_sms_template.description
     *
     * @return the value of supply_chain..smart_operation_sms_template.description
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public String getDescription() {
        return description;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public SmartOperationSmsTemplate withDescription(String description) {
        this.setDescription(description);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_sms_template.description
     *
     * @param description the value for supply_chain..smart_operation_sms_template.description
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_sms_template.user_id
     *
     * @return the value of supply_chain..smart_operation_sms_template.user_id
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public SmartOperationSmsTemplate withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_sms_template.user_id
     *
     * @param userId the value for supply_chain..smart_operation_sms_template.user_id
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_sms_template.create_time
     *
     * @return the value of supply_chain..smart_operation_sms_template.create_time
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public SmartOperationSmsTemplate withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_sms_template.create_time
     *
     * @param createTime the value for supply_chain..smart_operation_sms_template.create_time
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_sms_template.update_time
     *
     * @return the value of supply_chain..smart_operation_sms_template.update_time
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public SmartOperationSmsTemplate withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_sms_template.update_time
     *
     * @param updateTime the value for supply_chain..smart_operation_sms_template.update_time
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateName=").append(templateName);
        sb.append(", templateId=").append(templateId);
        sb.append(", templateType=").append(templateType);
        sb.append(", templateContent=").append(templateContent);
        sb.append(", description=").append(description);
        sb.append(", userId=").append(userId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SmartOperationSmsTemplate other = (SmartOperationSmsTemplate) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTemplateName() == null ? other.getTemplateName() == null : this.getTemplateName().equals(other.getTemplateName()))
            && (this.getTemplateId() == null ? other.getTemplateId() == null : this.getTemplateId().equals(other.getTemplateId()))
            && (this.getTemplateType() == null ? other.getTemplateType() == null : this.getTemplateType().equals(other.getTemplateType()))
            && (this.getTemplateContent() == null ? other.getTemplateContent() == null : this.getTemplateContent().equals(other.getTemplateContent()))
            && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTemplateName() == null) ? 0 : getTemplateName().hashCode());
        result = prime * result + ((getTemplateId() == null) ? 0 : getTemplateId().hashCode());
        result = prime * result + ((getTemplateType() == null) ? 0 : getTemplateType().hashCode());
        result = prime * result + ((getTemplateContent() == null) ? 0 : getTemplateContent().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Feb 25 14:35:00 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        templateName("template_name", "templateName", "VARCHAR", false),
        templateId("template_id", "templateId", "VARCHAR", false),
        templateType("template_type", "templateType", "VARCHAR", false),
        templateContent("template_content", "templateContent", "VARCHAR", false),
        description("description", "description", "VARCHAR", false),
        userId("user_id", "userId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Feb 25 14:35:00 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}