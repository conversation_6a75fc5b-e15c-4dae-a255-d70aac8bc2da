package com.chinamobile.operation.pojo.param;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2025/2/21 10:45
 */
@Data
public class ActivityListParam extends BasePageQuery {

    private String id;

    //活动名称
    private String name;

    //触达方式 1-短信 2-公众号 3-小程序
    private Integer type;

    //触发条件 1-单次触发 2-周期触发
    private Integer startCondition;

    //开始时间，对于单次触发就是触发时间
    private Date startTime;

    //结束时间(单次触发没有结束时间)
    private Date endTime;

    // 0-设计中 1-审批中 2-被驳回 3-待发布（审批通过） 4-未开始(已发布) 5-已开始 6-暂停中 7-已结束 8-关闭 9-审批列表全部状态（1,2,3）
    private Integer status;

    //创建人名称，模糊搜索
    private String creatorName;

}
