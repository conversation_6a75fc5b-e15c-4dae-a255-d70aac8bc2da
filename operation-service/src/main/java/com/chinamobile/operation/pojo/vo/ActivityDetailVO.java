package com.chinamobile.operation.pojo.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2025/2/26 10:24
 */
@Data
public class ActivityDetailVO {

    //id
    private String id;

    //活动名称
    private String name;

    //目标群组信息
    private List<GroupInfo> groupInfoList;

    //触发类型 触达方式 1-短信 2-公众号 3-小程序
    private Integer type;

    //触发内容,对于短信就是模板表id
    private String content;

    //模板id
    private String templateId;

    //模板名称
    private String templateName;

    //模板内容
    private String templateContent;

    //触发条件 1-单次触发 2-周期触发
    private Integer startCondition;

    //单次触发的时间，周期触发的开始时间
    private Date startTime;

    //周期触发的结束时间
    private Date endTime;

    //每周的哪几天触发，-1代表全部
    private String weekDays;

    //每月的哪几天触发,逗号分隔，-1代表全部
    private String monthDays;

    //周期触发的具体时间
    private String taskTime;


    @Data
    public static class GroupInfo{

        private String id;

        private String name;
    }
}
