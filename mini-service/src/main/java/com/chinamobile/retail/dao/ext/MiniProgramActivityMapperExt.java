package com.chinamobile.retail.dao.ext;

import com.chinamobile.retail.pojo.dto.ActivityDataRankDTO;
import com.chinamobile.retail.pojo.dto.ActivityWeeklyFunAwardCountDTO;
import com.chinamobile.retail.pojo.dto.MiniProgramActivityUserRankingDTO;
import com.chinamobile.retail.pojo.entity.MiniProgramActivityUser;
import com.chinamobile.retail.pojo.entity.SpuOfferingInfo;
import com.chinamobile.retail.pojo.handel.OrderInfoHandle;
import com.chinamobile.retail.pojo.mapper.SelectRankActivityUserAwardDTO;
import com.chinamobile.retail.pojo.param.miniprogram.ActivityWeeklyFunAwardCountParam;
import com.chinamobile.retail.pojo.param.miniprogram.PageActivityListParam;
import com.chinamobile.retail.pojo.param.miniprogram.RegionParam;
import com.chinamobile.retail.pojo.param.miniprogram.SelectActivityUsersParam;
import com.chinamobile.retail.pojo.vo.miniprogram.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 17:29
 * @description TODO
 */
@Mapper
public interface MiniProgramActivityMapperExt {

    List<PageActivityVO> getListFrontPage(@Param("activityType")Integer activityType,@Param("status")Integer status,@Param("isParticipate")Boolean isParticipate, @Param("userId")String userId,@Param("beId")String beId,@Param("location")String location);
    List<PageActivityVO> pageActivityList(PageActivityListParam param);

    Long countActivityList(PageActivityListParam param);

    ActivityStatisticsVO getTotalNum(String activityId,String userId);

    Long getActivityPersonNum(@Param("roleTypeList") List<String> roleTypeList);

    Long getActivityPersonNum(@Param("activityId") String activityId);
    Long getActivityPersonNumSignUp(@Param("activityId") String activityId);
    Long getPersonNum(@Param("roleTypeList") List<String> roleTypeList, @Param("regions")List<RegionParam> regionParamList);

    List<MiniProgramActivityUser> selectActivityUsers(SelectActivityUsersParam param);

    List<MiniProgramActivityUserRankingDTO> selectRankActivityUserAwards(SelectRankActivityUserAwardDTO param);

    Integer getOrderNum(@Param("startTime") Date startTime,@Param("endTime") Date endTime,@Param("spuCodeList")List<String> spuCodeList,@Param("offeringClassList") List<String> offeringClassList,@Param("userId")String userId);
    List<ActivityAwardListVO> selectActivityAwardList(@Param("userId") String userId);
    List<ActivityDataRankDTO> getDataRankList(@Param("activityId") String activityId,@Param("name") String name);
    void updateDataRank(@Param("ranking") Integer ranking);
    List<OrderInfoHandle> getDataRankOrderInfo(@Param("startTime") String startTime,@Param("endTime") String endTime,@Param("activityId") String activityId, @Param("userId") String userId, @Param("offeringClassList") List<String> offeringClassList, @Param("spuCodeList") List<String> spuCodeList, @Param("orderType") String orderType, @Param("businessCodes") List<String> businessCodes);
    List<ActivityAccessVO> getDataRankVisitCount(String activityId);

    List<ActivityProvinceVO> getAllRegions();
    List<SpuOfferingInfo> searchSpuByCode(@Param("spuCode") String spuCode);
    List<SpuOfferingInfo> searchSpuAllByCode(@Param("spuCode") String spuCode);
    List<ActivityExtraRankAwardVO> getActivityExtraRankAward(@Param("activityId") String activityId);
    List<ActivityExtraWeeklyAwardVO> getActivityExtraWeeklyAward(@Param("activityId") String activityId);
    Integer getNotReceivedAwardNum(String userId);
    List<PageActivityVO> searchMiniActivity(@Param("keyWord")String keyWord,@Param("provinceCode")String provinceCode);
    //主页搜索活动
    List<HomeSearchVO> homeSearchMiniActivity(@Param("keyWord")String keyWord, @Param("provinceCode")String provinceCode, @Param("cityCode")String cityCode);

    List<ActivityWeeklyFunAwardCountDTO> getActivityWeeklyFunAwardCount(ActivityWeeklyFunAwardCountParam param);
}
