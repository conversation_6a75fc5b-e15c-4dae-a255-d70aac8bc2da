package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.AtomOfferingInfo;
import com.chinamobile.retail.pojo.entity.AtomOfferingInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AtomOfferingInfoMapper {
    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    long countByExample(AtomOfferingInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    int deleteByExample(AtomOfferingInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    int insert(AtomOfferingInfo record);

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    int insertSelective(AtomOfferingInfo record);

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    List<AtomOfferingInfo> selectByExample(AtomOfferingInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    AtomOfferingInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    int updateByExampleSelective(@Param("record") AtomOfferingInfo record, @Param("example") AtomOfferingInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    int updateByExample(@Param("record") AtomOfferingInfo record, @Param("example") AtomOfferingInfoExample example);

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    int updateByPrimaryKeySelective(AtomOfferingInfo record);

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    int updateByPrimaryKey(AtomOfferingInfo record);

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    int batchInsert(@Param("list") List<AtomOfferingInfo> list);

    /**
     *
     * @mbg.generated Mon Sep 09 10:14:02 CST 2024
     */
    int batchInsertSelective(@Param("list") List<AtomOfferingInfo> list, @Param("selective") AtomOfferingInfo.Column ... selective);
}