package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.LogisticsInfo;
import com.chinamobile.retail.pojo.entity.LogisticsInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface LogisticsInfoMapper {
    /**
     *
     * @mbg.generated Wed Jul 16 17:11:15 CST 2025
     */
    long countByExample(LogisticsInfoExample example);

    /**
     *
     * @mbg.generated Wed Jul 16 17:11:15 CST 2025
     */
    int deleteByExample(LogisticsInfoExample example);

    /**
     *
     * @mbg.generated Wed Jul 16 17:11:15 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Jul 16 17:11:15 CST 2025
     */
    int insert(LogisticsInfo record);

    /**
     *
     * @mbg.generated Wed Jul 16 17:11:15 CST 2025
     */
    int insertSelective(LogisticsInfo record);

    /**
     *
     * @mbg.generated Wed Jul 16 17:11:15 CST 2025
     */
    List<LogisticsInfo> selectByExample(LogisticsInfoExample example);

    /**
     *
     * @mbg.generated Wed Jul 16 17:11:15 CST 2025
     */
    LogisticsInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Jul 16 17:11:15 CST 2025
     */
    int updateByExampleSelective(@Param("record") LogisticsInfo record, @Param("example") LogisticsInfoExample example);

    /**
     *
     * @mbg.generated Wed Jul 16 17:11:15 CST 2025
     */
    int updateByExample(@Param("record") LogisticsInfo record, @Param("example") LogisticsInfoExample example);

    /**
     *
     * @mbg.generated Wed Jul 16 17:11:15 CST 2025
     */
    int updateByPrimaryKeySelective(LogisticsInfo record);

    /**
     *
     * @mbg.generated Wed Jul 16 17:11:15 CST 2025
     */
    int updateByPrimaryKey(LogisticsInfo record);

    /**
     *
     * @mbg.generated Wed Jul 16 17:11:15 CST 2025
     */
    int batchInsert(@Param("list") List<LogisticsInfo> list);

    /**
     *
     * @mbg.generated Wed Jul 16 17:11:15 CST 2025
     */
    int batchInsertSelective(@Param("list") List<LogisticsInfo> list, @Param("selective") LogisticsInfo.Column ... selective);
}