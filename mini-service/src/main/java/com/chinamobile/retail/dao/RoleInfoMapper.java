package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.RoleInfo;
import com.chinamobile.retail.pojo.entity.RoleInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface RoleInfoMapper {
    /**
     *
     * @mbg.generated Wed Jun 04 16:17:11 CST 2025
     */
    long countByExample(RoleInfoExample example);

    /**
     *
     * @mbg.generated Wed Jun 04 16:17:11 CST 2025
     */
    int deleteByExample(RoleInfoExample example);

    /**
     *
     * @mbg.generated Wed Jun 04 16:17:11 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Jun 04 16:17:11 CST 2025
     */
    int insert(RoleInfo record);

    /**
     *
     * @mbg.generated Wed Jun 04 16:17:11 CST 2025
     */
    int insertSelective(RoleInfo record);

    /**
     *
     * @mbg.generated Wed Jun 04 16:17:11 CST 2025
     */
    List<RoleInfo> selectByExample(RoleInfoExample example);

    /**
     *
     * @mbg.generated Wed Jun 04 16:17:11 CST 2025
     */
    RoleInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Jun 04 16:17:11 CST 2025
     */
    int updateByExampleSelective(@Param("record") RoleInfo record, @Param("example") RoleInfoExample example);

    /**
     *
     * @mbg.generated Wed Jun 04 16:17:11 CST 2025
     */
    int updateByExample(@Param("record") RoleInfo record, @Param("example") RoleInfoExample example);

    /**
     *
     * @mbg.generated Wed Jun 04 16:17:11 CST 2025
     */
    int updateByPrimaryKeySelective(RoleInfo record);

    /**
     *
     * @mbg.generated Wed Jun 04 16:17:11 CST 2025
     */
    int updateByPrimaryKey(RoleInfo record);

    /**
     *
     * @mbg.generated Wed Jun 04 16:17:11 CST 2025
     */
    int batchInsert(@Param("list") List<RoleInfo> list);

    /**
     *
     * @mbg.generated Wed Jun 04 16:17:11 CST 2025
     */
    int batchInsertSelective(@Param("list") List<RoleInfo> list, @Param("selective") RoleInfo.Column ... selective);
}