package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.SkuRoleRelation;
import com.chinamobile.retail.pojo.entity.SkuRoleRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SkuRoleRelationMapper {
    /**
     *
     * @mbg.generated Tue Sep 06 15:14:36 CST 2022
     */
    long countByExample(SkuRoleRelationExample example);

    /**
     *
     * @mbg.generated Tue Sep 06 15:14:36 CST 2022
     */
    int deleteByExample(SkuRoleRelationExample example);

    /**
     *
     * @mbg.generated Tue Sep 06 15:14:36 CST 2022
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Sep 06 15:14:36 CST 2022
     */
    int insert(SkuRoleRelation record);

    /**
     *
     * @mbg.generated Tue Sep 06 15:14:36 CST 2022
     */
    int insertSelective(SkuRoleRelation record);

    /**
     *
     * @mbg.generated Tue Sep 06 15:14:36 CST 2022
     */
    List<SkuRoleRelation> selectByExample(SkuRoleRelationExample example);

    /**
     *
     * @mbg.generated Tue Sep 06 15:14:36 CST 2022
     */
    SkuRoleRelation selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Sep 06 15:14:36 CST 2022
     */
    int updateByExampleSelective(@Param("record") SkuRoleRelation record, @Param("example") SkuRoleRelationExample example);

    /**
     *
     * @mbg.generated Tue Sep 06 15:14:36 CST 2022
     */
    int updateByExample(@Param("record") SkuRoleRelation record, @Param("example") SkuRoleRelationExample example);

    /**
     *
     * @mbg.generated Tue Sep 06 15:14:36 CST 2022
     */
    int updateByPrimaryKeySelective(SkuRoleRelation record);

    /**
     *
     * @mbg.generated Tue Sep 06 15:14:36 CST 2022
     */
    int updateByPrimaryKey(SkuRoleRelation record);

    /**
     *
     * @mbg.generated Tue Sep 06 15:14:36 CST 2022
     */
    int batchInsert(@Param("list") List<SkuRoleRelation> list);

    /**
     *
     * @mbg.generated Tue Sep 06 15:14:36 CST 2022
     */
    int batchInsertSelective(@Param("list") List<SkuRoleRelation> list, @Param("selective") SkuRoleRelation.Column ... selective);
}