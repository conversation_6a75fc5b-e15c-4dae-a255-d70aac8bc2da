package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.RegionInfo;
import com.chinamobile.retail.pojo.entity.RegionInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface RegionInfoMapper {
    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    long countByExample(RegionInfoExample example);

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    int deleteByExample(RegionInfoExample example);

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    int deleteByPrimaryKey(String regionId);

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    int insert(RegionInfo record);

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    int insertSelective(RegionInfo record);

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    List<RegionInfo> selectByExample(RegionInfoExample example);

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    RegionInfo selectByPrimaryKey(String regionId);

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    int updateByExampleSelective(@Param("record") RegionInfo record, @Param("example") RegionInfoExample example);

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    int updateByExample(@Param("record") RegionInfo record, @Param("example") RegionInfoExample example);

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    int updateByPrimaryKeySelective(RegionInfo record);

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    int updateByPrimaryKey(RegionInfo record);

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    int batchInsert(@Param("list") List<RegionInfo> list);

    /**
     *
     * @mbg.generated Mon May 12 11:21:18 CST 2025
     */
    int batchInsertSelective(@Param("list") List<RegionInfo> list, @Param("selective") RegionInfo.Column ... selective);
}