package com.chinamobile.retail.dao.ext;

import com.chinamobile.retail.pojo.dto.PartnerPointItemDTO;
import com.chinamobile.retail.pojo.mapper.PartnerStatisticsDO;
import com.chinamobile.retail.pojo.param.PartnerPointQueryParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PartnerPointMapperExt {
    /**查询合伙人积分列表*/
    List<PartnerPointItemDTO> queryPartnerPointList(@Param("param") PartnerPointQueryParam param);

    void addAvailablePoint(String partnerId, String supplierId, long orderPoint,Integer channel);

    Long getMinAvailablePoint(List<String> partnerIdList, String supplierId);

    Long getSumAvailablePoint(List<String> partnerIdList, String supplierId);

    Long getAvailablePoint(String supplierId, String partnerId);

    List<String> getAllPartnerIdBySupplier(String supplierId, Integer channel);

    PartnerStatisticsDO getPartnerStatistics(String userId, Date startTime,Date endTime, Integer channel);
}