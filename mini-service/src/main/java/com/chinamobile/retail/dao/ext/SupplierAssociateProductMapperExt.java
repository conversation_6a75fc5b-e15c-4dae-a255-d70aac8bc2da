package com.chinamobile.retail.dao.ext;

import com.chinamobile.retail.pojo.dto.SupplierProductDTO;
import com.chinamobile.retail.pojo.entity.SupplierAssociateProduct;
import com.chinamobile.retail.pojo.param.PartnerPointQueryParam;
import com.chinamobile.retail.pojo.param.SupplierProductQueryParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SupplierAssociateProductMapperExt {
    /**查询供应商关联商品*/
    List<SupplierProductDTO> queryAssociateProducts(@Param("supplierId") String supplierId,@Param("key") String key);

    /**查询未被关联商品*/
    List<SupplierProductDTO> queryUnassociatedProducts(@Param("param") SupplierProductQueryParam param);

    /**查询不能被删除的关联商品*/
    List<SupplierProductDTO> queryCannotDelProduct(@Param("ids") List<String> ids);
}