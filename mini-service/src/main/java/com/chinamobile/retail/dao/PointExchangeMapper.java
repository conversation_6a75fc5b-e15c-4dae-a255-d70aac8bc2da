package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.PointExchange;
import com.chinamobile.retail.pojo.entity.PointExchangeExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PointExchangeMapper {
    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    long countByExample(PointExchangeExample example);

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    int deleteByExample(PointExchangeExample example);

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    int insert(PointExchange record);

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    int insertSelective(PointExchange record);

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    List<PointExchange> selectByExample(PointExchangeExample example);

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    PointExchange selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    int updateByExampleSelective(@Param("record") PointExchange record, @Param("example") PointExchangeExample example);

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    int updateByExample(@Param("record") PointExchange record, @Param("example") PointExchangeExample example);

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    int updateByPrimaryKeySelective(PointExchange record);

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    int updateByPrimaryKey(PointExchange record);

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    int batchInsert(@Param("list") List<PointExchange> list);

    /**
     *
     * @mbg.generated Tue Apr 18 17:42:11 CST 2023
     */
    int batchInsertSelective(@Param("list") List<PointExchange> list, @Param("selective") PointExchange.Column ... selective);
}