package com.chinamobile.retail.dao.ext;

import com.chinamobile.retail.pojo.param.miniprogram.ActivityDataRankParam;
import com.chinamobile.retail.pojo.param.miniprogram.PageInfoListParam;
import com.chinamobile.retail.pojo.param.miniprogram.PageKnowledgeListParam;
import com.chinamobile.retail.pojo.vo.miniprogram.InfoSpuItemVO;
import com.chinamobile.retail.pojo.vo.miniprogram.PageInfoVO;
import com.chinamobile.retail.pojo.vo.miniprogram.PageKnowledgeInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 */
@Mapper
public interface MiniProgramKnowledgeMapperExt {

    List<PageKnowledgeInfoVO> pageKnowledgeList(@Param("param") PageKnowledgeListParam param);

    Long countInfoList(PageInfoListParam param);

    List<InfoSpuItemVO> getInfoSpuList(@Param("infoId") String infoId);

    String getUserName(@Param("userId") String userId);
}
