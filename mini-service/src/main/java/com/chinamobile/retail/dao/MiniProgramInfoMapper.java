package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramInfo;
import com.chinamobile.retail.pojo.entity.MiniProgramInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramInfoMapper {
    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    long countByExample(MiniProgramInfoExample example);

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    int deleteByExample(MiniProgramInfoExample example);

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    int insert(MiniProgramInfo record);

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    int insertSelective(MiniProgramInfo record);

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    List<MiniProgramInfo> selectByExample(MiniProgramInfoExample example);

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    MiniProgramInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramInfo record, @Param("example") MiniProgramInfoExample example);

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    int updateByExample(@Param("record") MiniProgramInfo record, @Param("example") MiniProgramInfoExample example);

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    int updateByPrimaryKeySelective(MiniProgramInfo record);

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    int updateByPrimaryKey(MiniProgramInfo record);

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    int batchInsert(@Param("list") List<MiniProgramInfo> list);

    /**
     *
     * @mbg.generated Tue Dec 31 11:11:41 CST 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramInfo> list, @Param("selective") MiniProgramInfo.Column ... selective);
}