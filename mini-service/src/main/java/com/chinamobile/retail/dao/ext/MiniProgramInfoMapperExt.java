package com.chinamobile.retail.dao.ext;

import com.chinamobile.retail.pojo.entity.MiniProgramInfoActivity;
import com.chinamobile.retail.pojo.param.miniprogram.InfoSpuListParam;
import com.chinamobile.retail.pojo.param.miniprogram.KnowledgeHomeParam;
import com.chinamobile.retail.pojo.param.miniprogram.PageInfoListParam;
import com.chinamobile.retail.pojo.vo.miniprogram.InfoSpuItemVO;
import com.chinamobile.retail.pojo.vo.miniprogram.PageInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 */
@Mapper
public interface MiniProgramInfoMapperExt {

    List<PageInfoVO> pageInfoList(PageInfoListParam param);

    Long countInfoList(PageInfoListParam param);

    List<InfoSpuItemVO> getInfoSpuList(InfoSpuListParam param);
    List<InfoSpuItemVO> getInfoSpuListForWeb(@Param("infoId") String infoId);
    List<MiniProgramInfoActivity> getInfoActivityList(InfoSpuListParam param);

    String getUserName(@Param("userId") String userId);
    List<PageInfoVO>  getKnowlegeInfoList(@Param("param")KnowledgeHomeParam param);

    List<PageInfoVO> searchInfo(@Param("category") Integer category,@Param("keyWord") String keyWord,@Param("contentType")Integer contentType);
}
