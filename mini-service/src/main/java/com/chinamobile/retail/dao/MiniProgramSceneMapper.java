package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramScene;
import com.chinamobile.retail.pojo.entity.MiniProgramSceneExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramSceneMapper {
    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    long countByExample(MiniProgramSceneExample example);

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    int deleteByExample(MiniProgramSceneExample example);

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    int insert(MiniProgramScene record);

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    int insertSelective(MiniProgramScene record);

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    List<MiniProgramScene> selectByExample(MiniProgramSceneExample example);

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    MiniProgramScene selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramScene record, @Param("example") MiniProgramSceneExample example);

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    int updateByExample(@Param("record") MiniProgramScene record, @Param("example") MiniProgramSceneExample example);

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    int updateByPrimaryKeySelective(MiniProgramScene record);

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    int updateByPrimaryKey(MiniProgramScene record);

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    int batchInsert(@Param("list") List<MiniProgramScene> list);

    /**
     *
     * @mbg.generated Tue Dec 17 10:41:46 CST 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramScene> list, @Param("selective") MiniProgramScene.Column ... selective);
}