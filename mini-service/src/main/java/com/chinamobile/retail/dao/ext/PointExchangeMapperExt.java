package com.chinamobile.retail.dao.ext;

import com.chinamobile.retail.pojo.entity.PointExchange;
import com.chinamobile.retail.pojo.entity.PointExchangeExample;
import com.chinamobile.retail.pojo.mapper.PointDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PointExchangeMapperExt {

    List<PointDetailDO> getPointExchangeDetail(String partnerId, String supplierId,Integer channel);
}