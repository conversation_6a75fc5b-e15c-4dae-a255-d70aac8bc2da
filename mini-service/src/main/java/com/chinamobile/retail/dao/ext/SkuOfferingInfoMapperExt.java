package com.chinamobile.retail.dao.ext;

import com.chinamobile.retail.pojo.entity.SkuOfferingInfo;
import com.chinamobile.retail.pojo.entity.SkuOfferingInfoExample;
import com.chinamobile.retail.pojo.mapper.ProductBackListDO;
import com.chinamobile.retail.pojo.mapper.ProductFrontListDO;
import com.chinamobile.retail.pojo.mapper.SkuPointListDO;
import com.chinamobile.retail.pojo.vo.SkuPointListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SkuOfferingInfoMapperExt {


    int pageCountProductBackList(Integer partnerRoleId, List<Integer> pointStatus, List<String> pointSupplierId, List<String> productType, String queryParam, List<String> provinceCodeList, List<String> cityCodeList);

    List<ProductBackListDO> pageQueryProductBackList(Integer partnerRoleId, List<Integer> pointStatus, List<String> pointSupplierId, List<String> productType, String queryParam, List<String> provinceCodeList, List<String> cityCodeList, int start, Integer pageSize);

    String getRealProductName(String skuOfferingCode);

    List<SkuPointListDO> getSkuPointList(String spuCode, Integer roleType, String provinceCode, String cityCode);
}