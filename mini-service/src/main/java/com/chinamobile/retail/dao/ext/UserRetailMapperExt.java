package com.chinamobile.retail.dao.ext;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.retail.pojo.entity.UserRetail;
import com.chinamobile.retail.pojo.mapper.UserInfoListDO;
import com.chinamobile.retail.pojo.param.UserListParam;
import com.chinamobile.retail.pojo.vo.UserInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserRetailMapperExt {

    Page<UserInfoListDO> pageQueryUserInfoList(@Param("page") Page page, @Param("userDataQuery") UserListParam userDataQuery);

    long countUsers(@Param("userDataQuery") UserListParam userDataQuery);

    List<UserInfoListDO> queryUserInfoList(@Param("userDataQuery") UserListParam userDataQuery);
}
