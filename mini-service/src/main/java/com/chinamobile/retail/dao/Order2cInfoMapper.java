package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.Order2cInfo;
import com.chinamobile.retail.pojo.entity.Order2cInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface Order2cInfoMapper {
    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    long countByExample(Order2cInfoExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    int deleteByExample(Order2cInfoExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    int deleteByPrimaryKey(String orderId);

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    int insert(Order2cInfo record);

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    int insertSelective(Order2cInfo record);

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    List<Order2cInfo> selectByExample(Order2cInfoExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    Order2cInfo selectByPrimaryKey(String orderId);

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    int updateByExampleSelective(@Param("record") Order2cInfo record, @Param("example") Order2cInfoExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    int updateByExample(@Param("record") Order2cInfo record, @Param("example") Order2cInfoExample example);

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    int updateByPrimaryKeySelective(Order2cInfo record);

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    int updateByPrimaryKey(Order2cInfo record);

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    int batchInsert(@Param("list") List<Order2cInfo> list);

    /**
     *
     * @mbg.generated Fri Jul 26 10:24:02 CST 2024
     */
    int batchInsertSelective(@Param("list") List<Order2cInfo> list, @Param("selective") Order2cInfo.Column ... selective);
}