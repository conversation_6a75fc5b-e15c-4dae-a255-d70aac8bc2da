package com.chinamobile.retail.dao.ext;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.retail.pojo.mapper.OrderStatisticsByProvinceDO;
import com.chinamobile.retail.pojo.param.OrderInfoMiniProgramParam;
import com.chinamobile.retail.pojo.param.OrderInfoParam;
import com.chinamobile.retail.pojo.vo.AfterMarketOrderInfoVO;
import com.chinamobile.retail.pojo.vo.MiniProgramOrderDetailVO;
import com.chinamobile.retail.pojo.vo.OrderInfoMiniProgramVO;
import com.chinamobile.retail.pojo.vo.OrderInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/7 15:37
 * @description TODO
 */
@Mapper
public interface OrderInfoMapperExt {

    /**
     * 前端获取客户经理订单列表
     *
     * @param param
     * @return
     */
    List<OrderInfoVO> getOrderInfoList(OrderInfoParam param);

    /**
     * 极客小程序分页获取订单信息
     * @param page
     * @param orderInfoMiniProgramParam
     * @return
     */
    List<OrderInfoMiniProgramVO> listMiniProgramOrderInfo(@Param(value = "page") Page page,
                                                          @Param(value = "orderInfoMiniProgramParam")OrderInfoMiniProgramParam orderInfoMiniProgramParam);

    /**
     * 极客小程序获取订单详情
     * @param orderId
     * @return
     */
    MiniProgramOrderDetailVO getMiniProgramOrderDetail(String orderId);

    /**
     * 根据订单号获取列表需要的售后订单相关信息
     * @param orderId
     * @return
     */
    List<AfterMarketOrderInfoVO> getMiniProgramAfterMarketOrderDetail(String orderId);

    OrderStatisticsByProvinceDO getOrderAreaCount(@Param("startTime")String startTime,
                                                        @Param("endTime")String endTime,
                                                        @Param("provinceName")String provinceName,
                                                        @Param("cityName")String cityName,
                                                        @Param("organizationCode")String organizationCode);

    OrderStatisticsByProvinceDO getOrderUserCount(@Param("startTime")String startTime,
                                                        @Param("endTime")String endTime,
                                                        @Param("roleType")String roleType,
                                                        @Param("userId")String userId);

}
