package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.UserMiniProgram;
import com.chinamobile.retail.pojo.entity.UserMiniProgramExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserMiniProgramMapper {
    /**
     *
     * @mbg.generated Fri Jun 27 00:40:06 CST 2025
     */
    long countByExample(UserMiniProgramExample example);

    /**
     *
     * @mbg.generated Fri Jun 27 00:40:06 CST 2025
     */
    int deleteByExample(UserMiniProgramExample example);

    /**
     *
     * @mbg.generated Fri Jun 27 00:40:06 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Jun 27 00:40:06 CST 2025
     */
    int insert(UserMiniProgram record);

    /**
     *
     * @mbg.generated Fri Jun 27 00:40:06 CST 2025
     */
    int insertSelective(UserMiniProgram record);

    /**
     *
     * @mbg.generated Fri Jun 27 00:40:06 CST 2025
     */
    List<UserMiniProgram> selectByExample(UserMiniProgramExample example);

    /**
     *
     * @mbg.generated Fri Jun 27 00:40:06 CST 2025
     */
    UserMiniProgram selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Jun 27 00:40:06 CST 2025
     */
    int updateByExampleSelective(@Param("record") UserMiniProgram record, @Param("example") UserMiniProgramExample example);

    /**
     *
     * @mbg.generated Fri Jun 27 00:40:06 CST 2025
     */
    int updateByExample(@Param("record") UserMiniProgram record, @Param("example") UserMiniProgramExample example);

    /**
     *
     * @mbg.generated Fri Jun 27 00:40:06 CST 2025
     */
    int updateByPrimaryKeySelective(UserMiniProgram record);

    /**
     *
     * @mbg.generated Fri Jun 27 00:40:06 CST 2025
     */
    int updateByPrimaryKey(UserMiniProgram record);

    /**
     *
     * @mbg.generated Fri Jun 27 00:40:06 CST 2025
     */
    int batchInsert(@Param("list") List<UserMiniProgram> list);

    /**
     *
     * @mbg.generated Fri Jun 27 00:40:06 CST 2025
     */
    int batchInsertSelective(@Param("list") List<UserMiniProgram> list, @Param("selective") UserMiniProgram.Column ... selective);
}