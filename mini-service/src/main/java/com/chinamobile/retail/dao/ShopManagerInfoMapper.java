package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.ShopManagerInfo;
import com.chinamobile.retail.pojo.entity.ShopManagerInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ShopManagerInfoMapper {
    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    long countByExample(ShopManagerInfoExample example);

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    int deleteByExample(ShopManagerInfoExample example);

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    int insert(ShopManagerInfo record);

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    int insertSelective(ShopManagerInfo record);

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    List<ShopManagerInfo> selectByExample(ShopManagerInfoExample example);

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    ShopManagerInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    int updateByExampleSelective(@Param("record") ShopManagerInfo record, @Param("example") ShopManagerInfoExample example);

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    int updateByExample(@Param("record") ShopManagerInfo record, @Param("example") ShopManagerInfoExample example);

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    int updateByPrimaryKeySelective(ShopManagerInfo record);

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    int updateByPrimaryKey(ShopManagerInfo record);

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    int batchInsert(@Param("list") List<ShopManagerInfo> list);

    /**
     *
     * @mbg.generated Tue Jun 11 11:01:52 CST 2024
     */
    int batchInsertSelective(@Param("list") List<ShopManagerInfo> list, @Param("selective") ShopManagerInfo.Column ... selective);
}