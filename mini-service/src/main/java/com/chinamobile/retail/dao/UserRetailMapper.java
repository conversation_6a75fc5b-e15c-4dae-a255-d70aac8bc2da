package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.UserRetail;
import com.chinamobile.retail.pojo.entity.UserRetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserRetailMapper {
    long countByExample(UserRetailExample example);

    int deleteByExample(UserRetailExample example);

    int deleteByPrimaryKey(String id);

    int insert(UserRetail record);

    int insertSelective(UserRetail record);

    List<UserRetail> selectByExample(UserRetailExample example);

    UserRetail selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") UserRetail record, @Param("example") UserRetailExample example);

    int updateByExample(@Param("record") UserRetail record, @Param("example") UserRetailExample example);

    int updateByPrimaryKeySelective(UserRetail record);

    int updateByPrimaryKey(UserRetail record);

    int batchInsert(@Param("list") List<UserRetail> list);

    int batchInsertSelective(@Param("list") List<UserRetail> list, @Param("selective") UserRetail.Column ... selective);
}