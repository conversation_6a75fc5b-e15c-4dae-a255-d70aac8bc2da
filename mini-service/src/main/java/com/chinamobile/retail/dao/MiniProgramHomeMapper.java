package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramHome;
import com.chinamobile.retail.pojo.entity.MiniProgramHomeExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramHomeMapper {
    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    long countByExample(MiniProgramHomeExample example);

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    int deleteByExample(MiniProgramHomeExample example);

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    int insert(MiniProgramHome record);

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    int insertSelective(MiniProgramHome record);

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    List<MiniProgramHome> selectByExample(MiniProgramHomeExample example);

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    MiniProgramHome selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramHome record, @Param("example") MiniProgramHomeExample example);

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    int updateByExample(@Param("record") MiniProgramHome record, @Param("example") MiniProgramHomeExample example);

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    int updateByPrimaryKeySelective(MiniProgramHome record);

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    int updateByPrimaryKey(MiniProgramHome record);

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    int batchInsert(@Param("list") List<MiniProgramHome> list);

    /**
     *
     * @mbg.generated Wed Dec 04 09:54:29 CST 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramHome> list, @Param("selective") MiniProgramHome.Column ... selective);
}