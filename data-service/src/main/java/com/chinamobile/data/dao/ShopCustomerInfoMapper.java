package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.ShopCustomerInfo;
import com.chinamobile.data.pojo.entity.ShopCustomerInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ShopCustomerInfoMapper {
    /**
     *
     * @mbg.generated Wed Apr 26 17:14:07 CST 2023
     */
    long countByExample(ShopCustomerInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:07 CST 2023
     */
    int deleteByExample(ShopCustomerInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:07 CST 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:07 CST 2023
     */
    int insert(ShopCustomerInfo record);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:07 CST 2023
     */
    int insertSelective(ShopCustomerInfo record);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:07 CST 2023
     */
    List<ShopCustomerInfo> selectByExample(ShopCustomerInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:07 CST 2023
     */
    ShopCustomerInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:07 CST 2023
     */
    int updateByExampleSelective(@Param("record") ShopCustomerInfo record, @Param("example") ShopCustomerInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:07 CST 2023
     */
    int updateByExample(@Param("record") ShopCustomerInfo record, @Param("example") ShopCustomerInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:07 CST 2023
     */
    int updateByPrimaryKeySelective(ShopCustomerInfo record);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:07 CST 2023
     */
    int updateByPrimaryKey(ShopCustomerInfo record);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:07 CST 2023
     */
    int batchInsert(@Param("list") List<ShopCustomerInfo> list);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:07 CST 2023
     */
    int batchInsertSelective(@Param("list") List<ShopCustomerInfo> list, @Param("selective") ShopCustomerInfo.Column ... selective);
}