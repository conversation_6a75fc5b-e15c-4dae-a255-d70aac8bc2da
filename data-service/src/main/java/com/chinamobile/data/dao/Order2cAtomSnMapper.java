package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.Order2cAtomSn;
import com.chinamobile.data.pojo.entity.Order2cAtomSnExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface Order2cAtomSnMapper {
    /**
     *
     * @mbg.generated Wed Apr 13 11:08:00 CST 2022
     */
    long countByExample(Order2cAtomSnExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:00 CST 2022
     */
    int deleteByExample(Order2cAtomSnExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:00 CST 2022
     */
    int insert(Order2cAtomSn record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:00 CST 2022
     */
    int insertSelective(Order2cAtomSn record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:00 CST 2022
     */
    List<Order2cAtomSn> selectByExample(Order2cAtomSnExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:00 CST 2022
     */
    int updateByExampleSelective(@Param("record") Order2cAtomSn record, @Param("example") Order2cAtomSnExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:00 CST 2022
     */
    int updateByExample(@Param("record") Order2cAtomSn record, @Param("example") Order2cAtomSnExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:00 CST 2022
     */
    int batchInsert(@Param("list") List<Order2cAtomSn> list);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:00 CST 2022
     */
    int batchInsertSelective(@Param("list") List<Order2cAtomSn> list, @Param("selective") Order2cAtomSn.Column ... selective);
}