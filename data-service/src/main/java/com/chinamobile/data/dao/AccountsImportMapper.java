package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.AccountsImport;
import com.chinamobile.data.pojo.entity.AccountsImportExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AccountsImportMapper {
    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    long countByExample(AccountsImportExample example);

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    int deleteByExample(AccountsImportExample example);

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    int insert(AccountsImport record);

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    int insertSelective(AccountsImport record);

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    List<AccountsImport> selectByExample(AccountsImportExample example);

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    AccountsImport selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    int updateByExampleSelective(@Param("record") AccountsImport record, @Param("example") AccountsImportExample example);

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    int updateByExample(@Param("record") AccountsImport record, @Param("example") AccountsImportExample example);

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    int updateByPrimaryKeySelective(AccountsImport record);

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    int updateByPrimaryKey(AccountsImport record);

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    int batchInsert(@Param("list") List<AccountsImport> list);

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    int batchInsertSelective(@Param("list") List<AccountsImport> list, @Param("selective") AccountsImport.Column ... selective);
}