package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.User;
import com.chinamobile.data.pojo.entity.UserExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserMapper {
    /**
     *
     * @mbg.generated Tue Nov 29 20:35:24 CST 2022
     */
    long countByExample(UserExample example);

    /**
     *
     * @mbg.generated Tue Nov 29 20:35:24 CST 2022
     */
    int deleteByExample(UserExample example);

    /**
     *
     * @mbg.generated Tue Nov 29 20:35:24 CST 2022
     */
    int deleteByPrimaryKey(String userId);

    /**
     *
     * @mbg.generated Tue Nov 29 20:35:24 CST 2022
     */
    int insert(User record);

    /**
     *
     * @mbg.generated Tue Nov 29 20:35:24 CST 2022
     */
    int insertSelective(User record);

    /**
     *
     * @mbg.generated Tue Nov 29 20:35:24 CST 2022
     */
    List<User> selectByExample(UserExample example);

    /**
     *
     * @mbg.generated Tue Nov 29 20:35:24 CST 2022
     */
    User selectByPrimaryKey(String userId);

    /**
     *
     * @mbg.generated Tue Nov 29 20:35:24 CST 2022
     */
    int updateByExampleSelective(@Param("record") User record, @Param("example") UserExample example);

    /**
     *
     * @mbg.generated Tue Nov 29 20:35:24 CST 2022
     */
    int updateByExample(@Param("record") User record, @Param("example") UserExample example);

    /**
     *
     * @mbg.generated Tue Nov 29 20:35:24 CST 2022
     */
    int updateByPrimaryKeySelective(User record);

    /**
     *
     * @mbg.generated Tue Nov 29 20:35:24 CST 2022
     */
    int updateByPrimaryKey(User record);

    /**
     *
     * @mbg.generated Tue Nov 29 20:35:24 CST 2022
     */
    int batchInsert(@Param("list") List<User> list);

    /**
     *
     * @mbg.generated Tue Nov 29 20:35:24 CST 2022
     */
    int batchInsertSelective(@Param("list") List<User> list, @Param("selective") User.Column ... selective);
}