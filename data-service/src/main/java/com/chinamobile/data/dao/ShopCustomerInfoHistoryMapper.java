package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.ShopCustomerInfoHistory;
import com.chinamobile.data.pojo.entity.ShopCustomerInfoHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ShopCustomerInfoHistoryMapper {
    /**
     *
     * @mbg.generated Wed Apr 26 17:14:11 CST 2023
     */
    long countByExample(ShopCustomerInfoHistoryExample example);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:11 CST 2023
     */
    int deleteByExample(ShopCustomerInfoHistoryExample example);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:11 CST 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:11 CST 2023
     */
    int insert(ShopCustomerInfoHistory record);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:11 CST 2023
     */
    int insertSelective(ShopCustomerInfoHistory record);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:11 CST 2023
     */
    List<ShopCustomerInfoHistory> selectByExample(ShopCustomerInfoHistoryExample example);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:11 CST 2023
     */
    ShopCustomerInfoHistory selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:11 CST 2023
     */
    int updateByExampleSelective(@Param("record") ShopCustomerInfoHistory record, @Param("example") ShopCustomerInfoHistoryExample example);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:11 CST 2023
     */
    int updateByExample(@Param("record") ShopCustomerInfoHistory record, @Param("example") ShopCustomerInfoHistoryExample example);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:11 CST 2023
     */
    int updateByPrimaryKeySelective(ShopCustomerInfoHistory record);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:11 CST 2023
     */
    int updateByPrimaryKey(ShopCustomerInfoHistory record);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:11 CST 2023
     */
    int batchInsert(@Param("list") List<ShopCustomerInfoHistory> list);

    /**
     *
     * @mbg.generated Wed Apr 26 17:14:11 CST 2023
     */
    int batchInsertSelective(@Param("list") List<ShopCustomerInfoHistory> list, @Param("selective") ShopCustomerInfoHistory.Column ... selective);
}