package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.SkuOfferingInfo;
import com.chinamobile.data.pojo.entity.SkuOfferingInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SkuOfferingInfoMapper {
    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    long countByExample(SkuOfferingInfoExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    int deleteByExample(SkuOfferingInfoExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    int insert(SkuOfferingInfo record);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    int insertSelective(SkuOfferingInfo record);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    List<SkuOfferingInfo> selectByExample(SkuOfferingInfoExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    SkuOfferingInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    int updateByExampleSelective(@Param("record") SkuOfferingInfo record, @Param("example") SkuOfferingInfoExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    int updateByExample(@Param("record") SkuOfferingInfo record, @Param("example") SkuOfferingInfoExample example);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    int updateByPrimaryKeySelective(SkuOfferingInfo record);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    int updateByPrimaryKey(SkuOfferingInfo record);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    int batchInsert(@Param("list") List<SkuOfferingInfo> list);

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    int batchInsertSelective(@Param("list") List<SkuOfferingInfo> list, @Param("selective") SkuOfferingInfo.Column ... selective);
}