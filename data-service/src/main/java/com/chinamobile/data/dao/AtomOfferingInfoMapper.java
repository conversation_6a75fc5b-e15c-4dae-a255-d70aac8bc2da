package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.AtomOfferingInfo;
import com.chinamobile.data.pojo.entity.AtomOfferingInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AtomOfferingInfoMapper {
    /**
     *
     * @mbg.generated Wed Apr 13 11:06:15 CST 2022
     */
    long countByExample(AtomOfferingInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:06:15 CST 2022
     */
    int deleteByExample(AtomOfferingInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:06:15 CST 2022
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Apr 13 11:06:15 CST 2022
     */
    int insert(AtomOfferingInfo record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:06:15 CST 2022
     */
    int insertSelective(AtomOfferingInfo record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:06:15 CST 2022
     */
    List<AtomOfferingInfo> selectByExample(AtomOfferingInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:06:15 CST 2022
     */
    AtomOfferingInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Apr 13 11:06:15 CST 2022
     */
    int updateByExampleSelective(@Param("record") AtomOfferingInfo record, @Param("example") AtomOfferingInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:06:15 CST 2022
     */
    int updateByExample(@Param("record") AtomOfferingInfo record, @Param("example") AtomOfferingInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:06:15 CST 2022
     */
    int updateByPrimaryKeySelective(AtomOfferingInfo record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:06:15 CST 2022
     */
    int updateByPrimaryKey(AtomOfferingInfo record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:06:15 CST 2022
     */
    int batchInsert(@Param("list") List<AtomOfferingInfo> list);

    /**
     *
     * @mbg.generated Wed Apr 13 11:06:15 CST 2022
     */
    int batchInsertSelective(@Param("list") List<AtomOfferingInfo> list, @Param("selective") AtomOfferingInfo.Column ... selective);
}