package com.chinamobile.data.dao.ext;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.data.pojo.mapper.ShopDistributorDO;
import com.chinamobile.data.pojo.mapper.ShopRegisterUserDO;
import com.chinamobile.data.pojo.param.PageShopDistributorParam;
import com.chinamobile.data.pojo.param.PageShopRegisterUserParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/27
 * @description 商城客户信息mapper扩展类
 */
public interface ShopCustomerInfoHistoryMapperExt {

    /**
     * 分页获取分销员信息
     *
     * @param page
     * @param pageShopDistributorParam
     * @return
     */
    List<ShopDistributorDO> listShopDistributor(@Param("page") Page page, @Param("pageShopDistributorParam") PageShopDistributorParam pageShopDistributorParam);


    /**
     * 获取分销员信息
     *
     * @param pageShopDistributorParam
     * @return
     */
    List<ShopDistributorDO> listShopDistributor(@Param("pageShopDistributorParam") PageShopDistributorParam pageShopDistributorParam);

    /**
     * 分页获取注册用户信息
     *
     * @param page
     * @param pageShopRegisterUserParam
     * @return
     */
    List<ShopRegisterUserDO> listShopRegisterUser(@Param("page") Page page, @Param("pageShopRegisterUserParam") PageShopRegisterUserParam pageShopRegisterUserParam);


    /**
     * 获取注册用户信息
     *
     * @param pageShopRegisterUserParam
     * @return
     */
    List<ShopRegisterUserDO> listShopRegisterUser(@Param("pageShopRegisterUserParam") PageShopRegisterUserParam pageShopRegisterUserParam);
}
