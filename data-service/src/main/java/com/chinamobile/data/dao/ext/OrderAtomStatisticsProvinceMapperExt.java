package com.chinamobile.data.dao.ext;

import com.chinamobile.data.pojo.mapper.OrderStatisticsByProvinceDO;
import com.chinamobile.data.pojo.mapper.OrderStatisticsListDO;
import com.chinamobile.data.pojo.mapper.OrderStatisticsScreenDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

/**
 * created by liuxiang on 2022/4/14 15:47
 */
@Mapper
public interface OrderAtomStatisticsProvinceMapperExt {

    public OrderStatisticsScreenDO getSumData(Date startTime, Date endTime);

    List<OrderStatisticsByProvinceDO> getProvinceData(Date startTime, Date endTime);

    List<OrderStatisticsListDO> getMonthGroupData(Date startTime, Date endTime,String areaCode);

    List<OrderStatisticsListDO> getDayGroupData(Date startTime, Date endTime,String areaCode);
}
