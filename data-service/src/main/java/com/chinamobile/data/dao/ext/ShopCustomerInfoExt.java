package com.chinamobile.data.dao.ext;

import org.apache.ibatis.annotations.Mapper;

import java.util.Date;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2023/5/5 16:02
 * @description: 注册用户拓展类
 **/
@Mapper
public interface ShopCustomerInfoExt {

    /**
     * 获取注册用户数量
     * @param startTime
     * @param endTime
     * @return
     */
    Long getCustomerCount(Date startTime, Date endTime);

    /**
     * 根据省份获取省用户数据
     * @param startTime
     * @param endTime
     * @param landName
     * @param areaType
     * @return
     */
    Long getCustomerCountByProvince(Date startTime, Date endTime, String landName,Integer areaType);
}
