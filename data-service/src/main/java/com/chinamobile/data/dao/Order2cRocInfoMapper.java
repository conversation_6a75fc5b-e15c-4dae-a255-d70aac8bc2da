package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.Order2cRocInfo;
import com.chinamobile.data.pojo.entity.Order2cRocInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface Order2cRocInfoMapper {
    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    long countByExample(Order2cRocInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    int deleteByExample(Order2cRocInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    int deleteByPrimaryKey(String refundOrderId);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    int insert(Order2cRocInfo record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    int insertSelective(Order2cRocInfo record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    List<Order2cRocInfo> selectByExample(Order2cRocInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    Order2cRocInfo selectByPrimaryKey(String refundOrderId);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    int updateByExampleSelective(@Param("record") Order2cRocInfo record, @Param("example") Order2cRocInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    int updateByExample(@Param("record") Order2cRocInfo record, @Param("example") Order2cRocInfoExample example);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    int updateByPrimaryKeySelective(Order2cRocInfo record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    int updateByPrimaryKey(Order2cRocInfo record);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    int batchInsert(@Param("list") List<Order2cRocInfo> list);

    /**
     *
     * @mbg.generated Wed Apr 13 11:08:04 CST 2022
     */
    int batchInsertSelective(@Param("list") List<Order2cRocInfo> list, @Param("selective") Order2cRocInfo.Column ... selective);
}