package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.OrderAtomStatisticsSpu;
import com.chinamobile.data.pojo.entity.OrderAtomStatisticsSpuExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OrderAtomStatisticsSpuMapper {
    /**
     *
     * @mbg.generated Fri Apr 22 15:30:50 CST 2022
     */
    long countByExample(OrderAtomStatisticsSpuExample example);

    /**
     *
     * @mbg.generated Fri Apr 22 15:30:50 CST 2022
     */
    int deleteByExample(OrderAtomStatisticsSpuExample example);

    /**
     *
     * @mbg.generated Fri Apr 22 15:30:50 CST 2022
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Apr 22 15:30:50 CST 2022
     */
    int insert(OrderAtomStatisticsSpu record);

    /**
     *
     * @mbg.generated Fri Apr 22 15:30:50 CST 2022
     */
    int insertSelective(OrderAtomStatisticsSpu record);

    /**
     *
     * @mbg.generated Fri Apr 22 15:30:50 CST 2022
     */
    List<OrderAtomStatisticsSpu> selectByExample(OrderAtomStatisticsSpuExample example);

    /**
     *
     * @mbg.generated Fri Apr 22 15:30:50 CST 2022
     */
    OrderAtomStatisticsSpu selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Fri Apr 22 15:30:50 CST 2022
     */
    int updateByExampleSelective(@Param("record") OrderAtomStatisticsSpu record, @Param("example") OrderAtomStatisticsSpuExample example);

    /**
     *
     * @mbg.generated Fri Apr 22 15:30:50 CST 2022
     */
    int updateByExample(@Param("record") OrderAtomStatisticsSpu record, @Param("example") OrderAtomStatisticsSpuExample example);

    /**
     *
     * @mbg.generated Fri Apr 22 15:30:50 CST 2022
     */
    int updateByPrimaryKeySelective(OrderAtomStatisticsSpu record);

    /**
     *
     * @mbg.generated Fri Apr 22 15:30:50 CST 2022
     */
    int updateByPrimaryKey(OrderAtomStatisticsSpu record);

    /**
     *
     * @mbg.generated Fri Apr 22 15:30:50 CST 2022
     */
    int batchInsert(@Param("list") List<OrderAtomStatisticsSpu> list);

    /**
     *
     * @mbg.generated Fri Apr 22 15:30:50 CST 2022
     */
    int batchInsertSelective(@Param("list") List<OrderAtomStatisticsSpu> list, @Param("selective") OrderAtomStatisticsSpu.Column ... selective);
}