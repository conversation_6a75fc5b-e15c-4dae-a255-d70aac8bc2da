package com.chinamobile.data.dao;

import com.chinamobile.data.pojo.entity.Order2cAtomInfo;
import com.chinamobile.data.pojo.entity.Order2cAtomInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface Order2cAtomInfoMapper {
    /**
     *
     * @mbg.generated Mon Apr 03 11:10:39 CST 2023
     */
    long countByExample(Order2cAtomInfoExample example);

    /**
     *
     * @mbg.generated Mon Apr 03 11:10:39 CST 2023
     */
    int deleteByExample(Order2cAtomInfoExample example);

    /**
     *
     * @mbg.generated Mon Apr 03 11:10:39 CST 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Apr 03 11:10:39 CST 2023
     */
    int insert(Order2cAtomInfo record);

    /**
     *
     * @mbg.generated Mon Apr 03 11:10:39 CST 2023
     */
    int insertSelective(Order2cAtomInfo record);

    /**
     *
     * @mbg.generated Mon Apr 03 11:10:39 CST 2023
     */
    List<Order2cAtomInfo> selectByExample(Order2cAtomInfoExample example);

    /**
     *
     * @mbg.generated Mon Apr 03 11:10:39 CST 2023
     */
    Order2cAtomInfo selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Apr 03 11:10:39 CST 2023
     */
    int updateByExampleSelective(@Param("record") Order2cAtomInfo record, @Param("example") Order2cAtomInfoExample example);

    /**
     *
     * @mbg.generated Mon Apr 03 11:10:39 CST 2023
     */
    int updateByExample(@Param("record") Order2cAtomInfo record, @Param("example") Order2cAtomInfoExample example);

    /**
     *
     * @mbg.generated Mon Apr 03 11:10:39 CST 2023
     */
    int updateByPrimaryKeySelective(Order2cAtomInfo record);

    /**
     *
     * @mbg.generated Mon Apr 03 11:10:39 CST 2023
     */
    int updateByPrimaryKey(Order2cAtomInfo record);

    /**
     *
     * @mbg.generated Mon Apr 03 11:10:39 CST 2023
     */
    int batchInsert(@Param("list") List<Order2cAtomInfo> list);

    /**
     *
     * @mbg.generated Mon Apr 03 11:10:39 CST 2023
     */
    int batchInsertSelective(@Param("list") List<Order2cAtomInfo> list, @Param("selective") Order2cAtomInfo.Column ... selective);
}