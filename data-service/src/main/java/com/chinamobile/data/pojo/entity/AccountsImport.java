package com.chinamobile.data.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 存放导入的客户经理、注册用户数据
 *
 * <AUTHOR>
public class AccountsImport implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    private String id;

    /**
     * 所在省份
     *
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    private String province;

    /**
     * 地市
     *
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    private String cities;

    /**
     * 区县
     *
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    private String districts;

    /**
     * 客服编码
     *
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    private String custCode;

    /**
     * 注册姓名
     *
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    private String registerName;

    /**
     * 注册状态
     *
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    private String registerState;

    /**
     * 客户经理/注册用户的注册时间（xx年xx月xx日）
     *
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    private Date regTime;

    /**
     * 0：客户经理；1：注册用户
     *
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    private Integer type;

    /**
     * 导入时间 date
     *
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    private Date recordTime;

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..accounts_import.id
     *
     * @return the value of supply_chain..accounts_import.id
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public AccountsImport withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..accounts_import.id
     *
     * @param id the value for supply_chain..accounts_import.id
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..accounts_import.province
     *
     * @return the value of supply_chain..accounts_import.province
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public String getProvince() {
        return province;
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public AccountsImport withProvince(String province) {
        this.setProvince(province);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..accounts_import.province
     *
     * @param province the value for supply_chain..accounts_import.province
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * This method returns the value of the database column supply_chain..accounts_import.cities
     *
     * @return the value of supply_chain..accounts_import.cities
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public String getCities() {
        return cities;
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public AccountsImport withCities(String cities) {
        this.setCities(cities);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..accounts_import.cities
     *
     * @param cities the value for supply_chain..accounts_import.cities
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public void setCities(String cities) {
        this.cities = cities;
    }

    /**
     * This method returns the value of the database column supply_chain..accounts_import.districts
     *
     * @return the value of supply_chain..accounts_import.districts
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public String getDistricts() {
        return districts;
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public AccountsImport withDistricts(String districts) {
        this.setDistricts(districts);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..accounts_import.districts
     *
     * @param districts the value for supply_chain..accounts_import.districts
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public void setDistricts(String districts) {
        this.districts = districts;
    }

    /**
     * This method returns the value of the database column supply_chain..accounts_import.cust_code
     *
     * @return the value of supply_chain..accounts_import.cust_code
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public String getCustCode() {
        return custCode;
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public AccountsImport withCustCode(String custCode) {
        this.setCustCode(custCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..accounts_import.cust_code
     *
     * @param custCode the value for supply_chain..accounts_import.cust_code
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public void setCustCode(String custCode) {
        this.custCode = custCode;
    }

    /**
     * This method returns the value of the database column supply_chain..accounts_import.register_name
     *
     * @return the value of supply_chain..accounts_import.register_name
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public String getRegisterName() {
        return registerName;
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public AccountsImport withRegisterName(String registerName) {
        this.setRegisterName(registerName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..accounts_import.register_name
     *
     * @param registerName the value for supply_chain..accounts_import.register_name
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public void setRegisterName(String registerName) {
        this.registerName = registerName;
    }

    /**
     * This method returns the value of the database column supply_chain..accounts_import.register_state
     *
     * @return the value of supply_chain..accounts_import.register_state
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public String getRegisterState() {
        return registerState;
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public AccountsImport withRegisterState(String registerState) {
        this.setRegisterState(registerState);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..accounts_import.register_state
     *
     * @param registerState the value for supply_chain..accounts_import.register_state
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public void setRegisterState(String registerState) {
        this.registerState = registerState;
    }

    /**
     * This method returns the value of the database column supply_chain..accounts_import.reg_time
     *
     * @return the value of supply_chain..accounts_import.reg_time
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public Date getRegTime() {
        return regTime;
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public AccountsImport withRegTime(Date regTime) {
        this.setRegTime(regTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..accounts_import.reg_time
     *
     * @param regTime the value for supply_chain..accounts_import.reg_time
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public void setRegTime(Date regTime) {
        this.regTime = regTime;
    }

    /**
     * This method returns the value of the database column supply_chain..accounts_import.type
     *
     * @return the value of supply_chain..accounts_import.type
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public Integer getType() {
        return type;
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public AccountsImport withType(Integer type) {
        this.setType(type);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..accounts_import.type
     *
     * @param type the value for supply_chain..accounts_import.type
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method returns the value of the database column supply_chain..accounts_import.record_time
     *
     * @return the value of supply_chain..accounts_import.record_time
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public Date getRecordTime() {
        return recordTime;
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public AccountsImport withRecordTime(Date recordTime) {
        this.setRecordTime(recordTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..accounts_import.record_time
     *
     * @param recordTime the value for supply_chain..accounts_import.record_time
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", province=").append(province);
        sb.append(", cities=").append(cities);
        sb.append(", districts=").append(districts);
        sb.append(", custCode=").append(custCode);
        sb.append(", registerName=").append(registerName);
        sb.append(", registerState=").append(registerState);
        sb.append(", regTime=").append(regTime);
        sb.append(", type=").append(type);
        sb.append(", recordTime=").append(recordTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AccountsImport other = (AccountsImport) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getProvince() == null ? other.getProvince() == null : this.getProvince().equals(other.getProvince()))
            && (this.getCities() == null ? other.getCities() == null : this.getCities().equals(other.getCities()))
            && (this.getDistricts() == null ? other.getDistricts() == null : this.getDistricts().equals(other.getDistricts()))
            && (this.getCustCode() == null ? other.getCustCode() == null : this.getCustCode().equals(other.getCustCode()))
            && (this.getRegisterName() == null ? other.getRegisterName() == null : this.getRegisterName().equals(other.getRegisterName()))
            && (this.getRegisterState() == null ? other.getRegisterState() == null : this.getRegisterState().equals(other.getRegisterState()))
            && (this.getRegTime() == null ? other.getRegTime() == null : this.getRegTime().equals(other.getRegTime()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getRecordTime() == null ? other.getRecordTime() == null : this.getRecordTime().equals(other.getRecordTime()));
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getProvince() == null) ? 0 : getProvince().hashCode());
        result = prime * result + ((getCities() == null) ? 0 : getCities().hashCode());
        result = prime * result + ((getDistricts() == null) ? 0 : getDistricts().hashCode());
        result = prime * result + ((getCustCode() == null) ? 0 : getCustCode().hashCode());
        result = prime * result + ((getRegisterName() == null) ? 0 : getRegisterName().hashCode());
        result = prime * result + ((getRegisterState() == null) ? 0 : getRegisterState().hashCode());
        result = prime * result + ((getRegTime() == null) ? 0 : getRegTime().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getRecordTime() == null) ? 0 : getRecordTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Jul 18 09:50:43 CST 2022
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        province("province", "province", "VARCHAR", false),
        cities("cities", "cities", "VARCHAR", false),
        districts("districts", "districts", "VARCHAR", false),
        custCode("cust_code", "custCode", "VARCHAR", false),
        registerName("register_name", "registerName", "VARCHAR", false),
        registerState("register_state", "registerState", "VARCHAR", false),
        regTime("reg_time", "regTime", "TIMESTAMP", false),
        type("type", "type", "INTEGER", false),
        recordTime("record_time", "recordTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Jul 18 09:50:43 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}