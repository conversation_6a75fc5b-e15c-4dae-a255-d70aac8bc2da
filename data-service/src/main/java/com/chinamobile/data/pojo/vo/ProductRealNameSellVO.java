package com.chinamobile.data.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/25
 * @description 商品真实名称销售数据
 */
@Data
public class ProductRealNameSellVO {
    /**
     * 实际销售产品名称
     */
    @Excel(name = "实际销售产品名称")
    private String realProductName;

    /**
     * 产品属性
     */
    @Excel(name = "产品属性")
    private String productProperty;

    /**
     * 范式名称
     */
    @Excel(name = "产品范式")
    private String offeringClassName;

    /**
     * 销售额(元)
     */
    @Excel(name = "销售额(元)")
    private BigDecimal realNameSellY;

    /**
     * 销售额(万元)
     */
    @Excel(name = "销售额(万元)")
    private BigDecimal realNameSellW;

    /**
     * 销售排名
     */
    @Excel(name = "销售排名")
    private Integer sellOrder;
}
