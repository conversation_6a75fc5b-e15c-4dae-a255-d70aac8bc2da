package com.chinamobile.data.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 省代码表
 *
 * <AUTHOR>
public class Province implements Serializable {
    /**
     *
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    private String provinceCode;

    /**
     *
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    private String provinceCompany;

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..province.province_code
     *
     * @return the value of supply_chain..province.province_code
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public Province withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province.province_code
     *
     * @param provinceCode the value for supply_chain..province.province_code
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    /**
     * This method returns the value of the database column supply_chain..province.province_company
     *
     * @return the value of supply_chain..province.province_company
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public String getProvinceCompany() {
        return provinceCompany;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public Province withProvinceCompany(String provinceCompany) {
        this.setProvinceCompany(provinceCompany);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province.province_company
     *
     * @param provinceCompany the value for supply_chain..province.province_company
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public void setProvinceCompany(String provinceCompany) {
        this.provinceCompany = provinceCompany;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", provinceCompany=").append(provinceCompany);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Province other = (Province) that;
        return (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getProvinceCompany() == null ? other.getProvinceCompany() == null : this.getProvinceCompany().equals(other.getProvinceCompany()));
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getProvinceCompany() == null) ? 0 : getProvinceCompany().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public enum Column {
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        provinceCompany("province_company", "provinceCompany", "VARCHAR", false);

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}