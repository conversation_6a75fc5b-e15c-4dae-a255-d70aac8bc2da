package com.chinamobile.data.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderInfoDTO {
    /**
     * 业务编码	业务编码名称
     * SyncGrpOrderInfo	集团订单同步
     * SyncIndividualOrderInfo	个人客户订单同步
     */
    private String businessCode;
    /**
     * 操作员编码
     * 取个人客户所属的客户经理的操作员编码;
     * 分享订购场景，取分享链接中的客户经理编码。（本场景暂不支持）
     * 自主注册个人客户，本字段为空。
     */
    private String createOperCode;
    /**
     * 操作员省工号
     */
    private String employeeNum;
    /**
     * 备注
     * 个人客户非标准化的要求在备注中说明。
     */
    private String remarks;
    /**
     * 业务订单流水号
     */
    private String orderId;
    /**
     * 预占流水号
     */
    private String bookId;
    /**
     * 订单状态
     */
    private Integer status;
    /**
     * 订单总金额
     */
    private String totalPrice;
    /**
     * 订单创建时间
     */
    private String createTime;
}
