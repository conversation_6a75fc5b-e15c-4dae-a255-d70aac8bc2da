package com.chinamobile.data.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/26
 * @description 部门销售额数据
 */
@Data
public class DepartmentSellVO {

    /**
     * 部门名称
     */
    @Excel(name = "部门")
    private String departmentName;

    /**
     * 省公司销售额(元)
     */
    @Excel(name = "省公司销售额(元)")
    private BigDecimal provinceSellY;

    /**
     * 省公司销售额(万元)
     */
    @Excel(name = "省公司销售额(万元)")
    private BigDecimal provinceSellW;

    /**
     * 物联网销售额(元)
     */
    @Excel(name = "物联网销售额(元)")
    private BigDecimal networkSellY;

    /**
     * 物联网销售额(万元)
     */
    @Excel(name = "物联网销售额(万元)")
    private BigDecimal networkSellW;
}
