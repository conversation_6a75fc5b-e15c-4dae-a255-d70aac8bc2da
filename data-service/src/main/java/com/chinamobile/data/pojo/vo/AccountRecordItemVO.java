package com.chinamobile.data.pojo.vo;

import lombok.Data;


/**
 * <AUTHOR>
 * @date 2022/4/25 11:04
 */
@Data
public class AccountRecordItemVO {

    /**
     * 主键
     *
     *
     * @mbg.generated Tue Jun 07 10:56:41 CST 2022
     */
    private String id;

    /**
     * 所在省份
     *
     *
     * @mbg.generated Tue Jun 07 10:56:41 CST 2022
     */
    private String province;

    /**
     * 地市
     *
     *
     * @mbg.generated Tue Jun 07 10:56:41 CST 2022
     */
    private String cities;

    /**
     * 区县
     *
     *
     * @mbg.generated Tue Jun 07 10:56:41 CST 2022
     */
    private String districts;

    /**
     * 注册id
     *
     *
     * @mbg.generated Tue Jun 07 10:56:41 CST 2022
     */
    private String custCode;

    /**
     * 注册姓名
     *
     *
     * @mbg.generated Tue Jun 07 10:56:41 CST 2022
     */
    private String registerName;

    /**
     * 注册状态
     *
     *
     * @mbg.generated Tue Jun 07 10:56:41 CST 2022
     */
    private String registerState;

    /**
     * 客户经理/注册用户的注册时间（xx年xx月xx日）
     *
     *
     * @mbg.generated Tue Jun 07 10:56:41 CST 2022
     */
    private String statisticDate;

    /**
     * 0：客户经理；1：注册用户
     *
     *
     * @mbg.generated Tue Jun 07 10:56:41 CST 2022
     */
    private Integer type;

    /**
     * 导入时间 date
     *
     *
     * @mbg.generated Tue Jun 07 10:56:41 CST 2022
     */
    private String importDate;

}
