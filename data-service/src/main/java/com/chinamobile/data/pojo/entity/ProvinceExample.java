package com.chinamobile.data.pojo.entity;

import java.util.ArrayList;
import java.util.List;

public class ProvinceExample {
    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public ProvinceExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public ProvinceExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public ProvinceExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public static Criteria newAndCreateCriteria() {
        ProvinceExample example = new ProvinceExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public ProvinceExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public ProvinceExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualToColumn(Province.Column column) {
            addCriterion(new StringBuilder("province_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualToColumn(Province.Column column) {
            addCriterion(new StringBuilder("province_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanColumn(Province.Column column) {
            addCriterion(new StringBuilder("province_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualToColumn(Province.Column column) {
            addCriterion(new StringBuilder("province_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanColumn(Province.Column column) {
            addCriterion(new StringBuilder("province_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualToColumn(Province.Column column) {
            addCriterion(new StringBuilder("province_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyIsNull() {
            addCriterion("province_company is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyIsNotNull() {
            addCriterion("province_company is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyEqualTo(String value) {
            addCriterion("province_company =", value, "provinceCompany");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyEqualToColumn(Province.Column column) {
            addCriterion(new StringBuilder("province_company = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyNotEqualTo(String value) {
            addCriterion("province_company <>", value, "provinceCompany");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyNotEqualToColumn(Province.Column column) {
            addCriterion(new StringBuilder("province_company <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyGreaterThan(String value) {
            addCriterion("province_company >", value, "provinceCompany");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyGreaterThanColumn(Province.Column column) {
            addCriterion(new StringBuilder("province_company > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("province_company >=", value, "provinceCompany");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyGreaterThanOrEqualToColumn(Province.Column column) {
            addCriterion(new StringBuilder("province_company >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyLessThan(String value) {
            addCriterion("province_company <", value, "provinceCompany");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyLessThanColumn(Province.Column column) {
            addCriterion(new StringBuilder("province_company < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyLessThanOrEqualTo(String value) {
            addCriterion("province_company <=", value, "provinceCompany");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyLessThanOrEqualToColumn(Province.Column column) {
            addCriterion(new StringBuilder("province_company <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyLike(String value) {
            addCriterion("province_company like", value, "provinceCompany");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyNotLike(String value) {
            addCriterion("province_company not like", value, "provinceCompany");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyIn(List<String> values) {
            addCriterion("province_company in", values, "provinceCompany");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyNotIn(List<String> values) {
            addCriterion("province_company not in", values, "provinceCompany");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyBetween(String value1, String value2) {
            addCriterion("province_company between", value1, value2, "provinceCompany");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyNotBetween(String value1, String value2) {
            addCriterion("province_company not between", value1, value2, "provinceCompany");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLikeInsensitive(String value) {
            addCriterion("upper(province_code) like", value.toUpperCase(), "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCompanyLikeInsensitive(String value) {
            addCriterion("upper(province_company) like", value.toUpperCase(), "provinceCompany");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Wed Apr 20 17:16:10 CST 2022
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        private ProvinceExample example;

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        protected Criteria(ProvinceExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        public ProvinceExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Wed Apr 20 17:16:10 CST 2022
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Wed Apr 20 17:16:10 CST 2022
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Wed Apr 20 17:16:10 CST 2022
         */
        void example(com.chinamobile.data.pojo.entity.ProvinceExample example);
    }
}