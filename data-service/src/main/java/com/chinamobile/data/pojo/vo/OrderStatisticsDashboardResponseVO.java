package com.chinamobile.data.pojo.vo;

import lombok.Data;

/**
 * created by liuxiang on 2022/4/14 14:16
 * 封装看板的订单返回信息
 */
@Data
public class OrderStatisticsDashboardResponseVO {

    //销售额
    private Long totalAtomOrderAmount;
    //订单数
    private Integer totalAtomOrderCount;
    //选择的时间内新增订单销售额
    private Long atomOrderAmount;
    //新增销售额环比增加的 比例
    private Double increateRatio;
    //本年新增销售额
    private Long currentYearAmount;
    //本时间段新增销售额/累计销售额
    private Double totalIncreateRatio;
    //本时间段新增销售额/本年销售额
    private Double currentYearIncreateRatio;
    //系统中首个订单的时间
    private String firstDataTime;


}
