<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.UserScreenMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.UserScreen">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="pwd" jdbcType="VARCHAR" property="pwd" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    <result column="account_level" jdbcType="INTEGER" property="accountLevel" />
    <result column="is_cancel" jdbcType="BIT" property="isCancel" />
    <result column="is_logoff" jdbcType="BIT" property="isLogoff" />
    <result column="is_admin" jdbcType="BIT" property="isAdmin" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="user_from" jdbcType="VARCHAR" property="userFrom" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="iot_type" jdbcType="INTEGER" property="iotType" />
    <result column="unified_status" jdbcType="INTEGER" property="unifiedStatus" />
    <result column="department_id" jdbcType="VARCHAR" property="departmentId" />
    <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
    <result column="old_job_number" jdbcType="VARCHAR" property="oldJobNumber" />
    <result column="new_job_number" jdbcType="VARCHAR" property="newJobNumber" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="location_name" jdbcType="VARCHAR" property="locationName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, name, pwd, phone, email, company, role_id, account_level, is_cancel, is_logoff, 
    is_admin, province, creator, create_time, update_time, user_from, user_type, iot_type, 
    unified_status, department_id, department_name, old_job_number, new_job_number, be_id, 
    location, location_name
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.UserScreenExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_screen
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from user_screen
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from user_screen
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.UserScreenExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from user_screen
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.UserScreen">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into user_screen (id, name, pwd, 
      phone, email, company, 
      role_id, account_level, is_cancel, 
      is_logoff, is_admin, province, 
      creator, create_time, update_time, 
      user_from, user_type, iot_type, 
      unified_status, department_id, department_name, 
      old_job_number, new_job_number, be_id, 
      location, location_name)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{pwd,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{company,jdbcType=VARCHAR}, 
      #{roleId,jdbcType=VARCHAR}, #{accountLevel,jdbcType=INTEGER}, #{isCancel,jdbcType=BIT}, 
      #{isLogoff,jdbcType=BIT}, #{isAdmin,jdbcType=BIT}, #{province,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{userFrom,jdbcType=VARCHAR}, #{userType,jdbcType=VARCHAR}, #{iotType,jdbcType=INTEGER}, 
      #{unifiedStatus,jdbcType=INTEGER}, #{departmentId,jdbcType=VARCHAR}, #{departmentName,jdbcType=VARCHAR}, 
      #{oldJobNumber,jdbcType=VARCHAR}, #{newJobNumber,jdbcType=VARCHAR}, #{beId,jdbcType=VARCHAR}, 
      #{location,jdbcType=VARCHAR}, #{locationName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.UserScreen">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into user_screen
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="pwd != null">
        pwd,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="company != null">
        company,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="accountLevel != null">
        account_level,
      </if>
      <if test="isCancel != null">
        is_cancel,
      </if>
      <if test="isLogoff != null">
        is_logoff,
      </if>
      <if test="isAdmin != null">
        is_admin,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="userFrom != null">
        user_from,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="iotType != null">
        iot_type,
      </if>
      <if test="unifiedStatus != null">
        unified_status,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="departmentName != null">
        department_name,
      </if>
      <if test="oldJobNumber != null">
        old_job_number,
      </if>
      <if test="newJobNumber != null">
        new_job_number,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="locationName != null">
        location_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="pwd != null">
        #{pwd,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        #{company,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="accountLevel != null">
        #{accountLevel,jdbcType=INTEGER},
      </if>
      <if test="isCancel != null">
        #{isCancel,jdbcType=BIT},
      </if>
      <if test="isLogoff != null">
        #{isLogoff,jdbcType=BIT},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=BIT},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userFrom != null">
        #{userFrom,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="iotType != null">
        #{iotType,jdbcType=INTEGER},
      </if>
      <if test="unifiedStatus != null">
        #{unifiedStatus,jdbcType=INTEGER},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="oldJobNumber != null">
        #{oldJobNumber,jdbcType=VARCHAR},
      </if>
      <if test="newJobNumber != null">
        #{newJobNumber,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="locationName != null">
        #{locationName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.UserScreenExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from user_screen
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    update user_screen
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.pwd != null">
        pwd = #{record.pwd,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.company != null">
        company = #{record.company,jdbcType=VARCHAR},
      </if>
      <if test="record.roleId != null">
        role_id = #{record.roleId,jdbcType=VARCHAR},
      </if>
      <if test="record.accountLevel != null">
        account_level = #{record.accountLevel,jdbcType=INTEGER},
      </if>
      <if test="record.isCancel != null">
        is_cancel = #{record.isCancel,jdbcType=BIT},
      </if>
      <if test="record.isLogoff != null">
        is_logoff = #{record.isLogoff,jdbcType=BIT},
      </if>
      <if test="record.isAdmin != null">
        is_admin = #{record.isAdmin,jdbcType=BIT},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.userFrom != null">
        user_from = #{record.userFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=VARCHAR},
      </if>
      <if test="record.iotType != null">
        iot_type = #{record.iotType,jdbcType=INTEGER},
      </if>
      <if test="record.unifiedStatus != null">
        unified_status = #{record.unifiedStatus,jdbcType=INTEGER},
      </if>
      <if test="record.departmentId != null">
        department_id = #{record.departmentId,jdbcType=VARCHAR},
      </if>
      <if test="record.departmentName != null">
        department_name = #{record.departmentName,jdbcType=VARCHAR},
      </if>
      <if test="record.oldJobNumber != null">
        old_job_number = #{record.oldJobNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.newJobNumber != null">
        new_job_number = #{record.newJobNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.locationName != null">
        location_name = #{record.locationName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    update user_screen
    set id = #{record.id,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      pwd = #{record.pwd,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      company = #{record.company,jdbcType=VARCHAR},
      role_id = #{record.roleId,jdbcType=VARCHAR},
      account_level = #{record.accountLevel,jdbcType=INTEGER},
      is_cancel = #{record.isCancel,jdbcType=BIT},
      is_logoff = #{record.isLogoff,jdbcType=BIT},
      is_admin = #{record.isAdmin,jdbcType=BIT},
      province = #{record.province,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      user_from = #{record.userFrom,jdbcType=VARCHAR},
      user_type = #{record.userType,jdbcType=VARCHAR},
      iot_type = #{record.iotType,jdbcType=INTEGER},
      unified_status = #{record.unifiedStatus,jdbcType=INTEGER},
      department_id = #{record.departmentId,jdbcType=VARCHAR},
      department_name = #{record.departmentName,jdbcType=VARCHAR},
      old_job_number = #{record.oldJobNumber,jdbcType=VARCHAR},
      new_job_number = #{record.newJobNumber,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      location_name = #{record.locationName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.UserScreen">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    update user_screen
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="pwd != null">
        pwd = #{pwd,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        company = #{company,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="accountLevel != null">
        account_level = #{accountLevel,jdbcType=INTEGER},
      </if>
      <if test="isCancel != null">
        is_cancel = #{isCancel,jdbcType=BIT},
      </if>
      <if test="isLogoff != null">
        is_logoff = #{isLogoff,jdbcType=BIT},
      </if>
      <if test="isAdmin != null">
        is_admin = #{isAdmin,jdbcType=BIT},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userFrom != null">
        user_from = #{userFrom,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="iotType != null">
        iot_type = #{iotType,jdbcType=INTEGER},
      </if>
      <if test="unifiedStatus != null">
        unified_status = #{unifiedStatus,jdbcType=INTEGER},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null">
        department_name = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="oldJobNumber != null">
        old_job_number = #{oldJobNumber,jdbcType=VARCHAR},
      </if>
      <if test="newJobNumber != null">
        new_job_number = #{newJobNumber,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="locationName != null">
        location_name = #{locationName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.UserScreen">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    update user_screen
    set name = #{name,jdbcType=VARCHAR},
      pwd = #{pwd,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      company = #{company,jdbcType=VARCHAR},
      role_id = #{roleId,jdbcType=VARCHAR},
      account_level = #{accountLevel,jdbcType=INTEGER},
      is_cancel = #{isCancel,jdbcType=BIT},
      is_logoff = #{isLogoff,jdbcType=BIT},
      is_admin = #{isAdmin,jdbcType=BIT},
      province = #{province,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      user_from = #{userFrom,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=VARCHAR},
      iot_type = #{iotType,jdbcType=INTEGER},
      unified_status = #{unifiedStatus,jdbcType=INTEGER},
      department_id = #{departmentId,jdbcType=VARCHAR},
      department_name = #{departmentName,jdbcType=VARCHAR},
      old_job_number = #{oldJobNumber,jdbcType=VARCHAR},
      new_job_number = #{newJobNumber,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      location_name = #{locationName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into user_screen
    (id, name, pwd, phone, email, company, role_id, account_level, is_cancel, is_logoff, 
      is_admin, province, creator, create_time, update_time, user_from, user_type, iot_type, 
      unified_status, department_id, department_name, old_job_number, new_job_number, 
      be_id, location, location_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.pwd,jdbcType=VARCHAR}, 
        #{item.phone,jdbcType=VARCHAR}, #{item.email,jdbcType=VARCHAR}, #{item.company,jdbcType=VARCHAR}, 
        #{item.roleId,jdbcType=VARCHAR}, #{item.accountLevel,jdbcType=INTEGER}, #{item.isCancel,jdbcType=BIT}, 
        #{item.isLogoff,jdbcType=BIT}, #{item.isAdmin,jdbcType=BIT}, #{item.province,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.userFrom,jdbcType=VARCHAR}, #{item.userType,jdbcType=VARCHAR}, #{item.iotType,jdbcType=INTEGER}, 
        #{item.unifiedStatus,jdbcType=INTEGER}, #{item.departmentId,jdbcType=VARCHAR}, 
        #{item.departmentName,jdbcType=VARCHAR}, #{item.oldJobNumber,jdbcType=VARCHAR}, 
        #{item.newJobNumber,jdbcType=VARCHAR}, #{item.beId,jdbcType=VARCHAR}, #{item.location,jdbcType=VARCHAR}, 
        #{item.locationName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Sep 25 10:39:20 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into user_screen (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'pwd'.toString() == column.value">
          #{item.pwd,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'email'.toString() == column.value">
          #{item.email,jdbcType=VARCHAR}
        </if>
        <if test="'company'.toString() == column.value">
          #{item.company,jdbcType=VARCHAR}
        </if>
        <if test="'role_id'.toString() == column.value">
          #{item.roleId,jdbcType=VARCHAR}
        </if>
        <if test="'account_level'.toString() == column.value">
          #{item.accountLevel,jdbcType=INTEGER}
        </if>
        <if test="'is_cancel'.toString() == column.value">
          #{item.isCancel,jdbcType=BIT}
        </if>
        <if test="'is_logoff'.toString() == column.value">
          #{item.isLogoff,jdbcType=BIT}
        </if>
        <if test="'is_admin'.toString() == column.value">
          #{item.isAdmin,jdbcType=BIT}
        </if>
        <if test="'province'.toString() == column.value">
          #{item.province,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'user_from'.toString() == column.value">
          #{item.userFrom,jdbcType=VARCHAR}
        </if>
        <if test="'user_type'.toString() == column.value">
          #{item.userType,jdbcType=VARCHAR}
        </if>
        <if test="'iot_type'.toString() == column.value">
          #{item.iotType,jdbcType=INTEGER}
        </if>
        <if test="'unified_status'.toString() == column.value">
          #{item.unifiedStatus,jdbcType=INTEGER}
        </if>
        <if test="'department_id'.toString() == column.value">
          #{item.departmentId,jdbcType=VARCHAR}
        </if>
        <if test="'department_name'.toString() == column.value">
          #{item.departmentName,jdbcType=VARCHAR}
        </if>
        <if test="'old_job_number'.toString() == column.value">
          #{item.oldJobNumber,jdbcType=VARCHAR}
        </if>
        <if test="'new_job_number'.toString() == column.value">
          #{item.newJobNumber,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'location'.toString() == column.value">
          #{item.location,jdbcType=VARCHAR}
        </if>
        <if test="'location_name'.toString() == column.value">
          #{item.locationName,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>