package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.management.relation.Role;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/3 15:06
 */
@Service
@Slf4j
public class CustomUserDetailsService implements UserDetailsService {
    @Autowired
    private RedisTemplate redisTemplate;
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        String userId = username;
        String redisKey = Constant.REDIS_KEY_USER_TOKEN + userId;
        //获取用户基本信息
        LoginIfo4Redis loginIfo4Redis = (LoginIfo4Redis)redisTemplate.opsForValue().get(redisKey);
        if(loginIfo4Redis == null){
            log.info("token已失效,token:{}",userId);
            throw new UsernameNotFoundException("");
        }

        return new User(
                username,
                username,
                getAuthorities(username)
        );
    }

    // 根据用户的角色返回 Authorities
    private Collection<? extends GrantedAuthority> getAuthorities(String username) {
        List<GrantedAuthority> authorities = new ArrayList<>();
        return authorities;
    }
}
