package com.chinamobile.iot.sc.config;

import com.chinamobile.iot.sc.pojo.config.Client;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11 10:37
 */
@Configuration
@ConfigurationProperties(prefix = "oauth2")
@Data
public class OAuth2ClientConfig {
    private List<Client> clients;
}
