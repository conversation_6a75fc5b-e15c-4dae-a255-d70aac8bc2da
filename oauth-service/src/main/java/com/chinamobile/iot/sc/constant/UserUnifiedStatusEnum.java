package com.chinamobile.iot.sc.constant;

/**
 * 分销中心合伙人角色类型
 */
public enum UserUnifiedStatusEnum {

    VALID(0,"正常"),
    LOCKED(1,"锁定"),
    INVALID(2,"未启用"),
    CANCEL(3,"注销"),
    ;

    public Integer code;
    public String name;

    UserUnifiedStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static UserUnifiedStatusEnum fromCode(Integer code){
        UserUnifiedStatusEnum[] values = UserUnifiedStatusEnum.values();
        for (UserUnifiedStatusEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return value;
            }
        }
        return null;
    }
}
