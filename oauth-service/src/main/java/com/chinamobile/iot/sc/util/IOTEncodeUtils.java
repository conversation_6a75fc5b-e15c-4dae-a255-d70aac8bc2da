package com.chinamobile.iot.sc.util;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @Author: YSC
 * @Date: 2021/11/18 16:54
 * @Description: IOT加解密工具类
 */
@Slf4j
public class IOTEncodeUtils {
    private static final String Algorithm = "DESede";

    public static String encryptIOTMessage(String secretContent, String stringKey){
        if (StringUtils.isEmpty(secretContent)) {
            return secretContent;
        }
        try {
            byte[] key = Arrays.copyOf(stringKey.getBytes(StandardCharsets.UTF_8), 24);
            SymmetricCrypto des = new SymmetricCrypto(SymmetricAlgorithm.DESede, key);
            //加密
            byte[] encrypt = des.encrypt(secretContent, StandardCharsets.UTF_8);
            String encrptStr = new String(HexUtil.encodeHex(encrypt));
            return Base64.encode(encrptStr);
        } catch (Exception e) {
            log.error("解密失败，解密原文:{}", secretContent);
            //解密失败直接返回密文
            return secretContent;
        }
    }

    public static String decryptIOTMessage(String secretContent, String stringKey) {
        if (StringUtils.isEmpty(secretContent)) {
            return secretContent;
        }
        try {
            byte[] hex = HexUtil.decodeHex(Base64.decodeStr(secretContent));

            byte[] key = Arrays.copyOf(stringKey.getBytes(StandardCharsets.UTF_8), 24);

            SymmetricCrypto des = new SymmetricCrypto(SymmetricAlgorithm.DESede, key);
            //解密
            byte[] decrypt = des.decrypt(hex);
            return new String(decrypt, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("解密失败，解密原文:{}", secretContent);
            //解密失败直接返回密文
            return secretContent;
        }
    }

    public static void main(String[] args) {
        System.out.println(decryptIOTMessage("MDYyRjgwNkNFNjRENEE1NzRBOEVFQTU3QjEzMTcxQjM=", "3D88F1C1AAE7"));
    }
}
