package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.entity.UserScreen;
import com.chinamobile.iot.sc.pojo.entity.UserScreenExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserScreenMapper {
    /**
     *
     * @mbg.generated Mon Sep 25 10:39:20 CST 2023
     */
    long countByExample(UserScreenExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:39:20 CST 2023
     */
    int deleteByExample(UserScreenExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:39:20 CST 2023
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Sep 25 10:39:20 CST 2023
     */
    int insert(UserScreen record);

    /**
     *
     * @mbg.generated Mon Sep 25 10:39:20 CST 2023
     */
    int insertSelective(UserScreen record);

    /**
     *
     * @mbg.generated Mon Sep 25 10:39:20 CST 2023
     */
    List<UserScreen> selectByExample(UserScreenExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:39:20 CST 2023
     */
    UserScreen selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Sep 25 10:39:20 CST 2023
     */
    int updateByExampleSelective(@Param("record") UserScreen record, @Param("example") UserScreenExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:39:20 CST 2023
     */
    int updateByExample(@Param("record") UserScreen record, @Param("example") UserScreenExample example);

    /**
     *
     * @mbg.generated Mon Sep 25 10:39:20 CST 2023
     */
    int updateByPrimaryKeySelective(UserScreen record);

    /**
     *
     * @mbg.generated Mon Sep 25 10:39:20 CST 2023
     */
    int updateByPrimaryKey(UserScreen record);

    /**
     *
     * @mbg.generated Mon Sep 25 10:39:20 CST 2023
     */
    int batchInsert(@Param("list") List<UserScreen> list);

    /**
     *
     * @mbg.generated Mon Sep 25 10:39:20 CST 2023
     */
    int batchInsertSelective(@Param("list") List<UserScreen> list, @Param("selective") UserScreen.Column ... selective);
}