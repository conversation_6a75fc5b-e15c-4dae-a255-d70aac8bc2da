package com.chinamobile.websocket.controller;

import com.chinamobile.iot.sc.common.KafkaTopic;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * created by l<PERSON><PERSON><PERSON> on 2023/10/17 09:46
 */
@RestController
@RequestMapping("/back")
public class TestController {

    @Autowired
    private KafkaTemplate<String, byte[]> kafkaTemplate;

    @RequestMapping("/test")
    public String test(String topic){
        if(StringUtils.isEmpty(topic)){
            return "topic不能为空";
        }
        ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), topic.getBytes());
        kafkaTemplate.send(record);
        return "ok";
    }
}
