#服务配置
server:
  port: 10001
  # 开启优雅下线
  shutdown: graceful
spring:
  profiles:
    active: test
  application:
    name: supply-chain-websocket-svc
#apollo apollo.bootstrap.enabled = true
apollo:
#  meta: http://10.12.4.11:8080
  meta: http://10.12.57.1:8080
  autoUpdateInjectedSpringProperties: true
  bootstrap:
    enabled: true
    namespaces: application.yml
#apollo app.id
app:
  id: supply-chain-websocket
# 暴露 shutdown和prometheus 接口
management:
  server:
    port: 8090
  endpoint:
    shutdown:
      enabled: true
    prometheus:
      enabled: true
  endpoints:
    web:
      exposure:
        include: '*'
  metrics:
    export:
      prometheus:
        enabled: true